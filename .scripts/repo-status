#!/usr/bin/env sh

git fetch origin main --quiet

if git branch -vv | grep --silent --extended-regexp "\[origin/main: (ahead|behind)"; then
    printf '\033[1;33m%s\033[0m \n' "[WARN] Your local 'main' branch is not in sync with the upstream 'origin/main' branch."
    printf "Please pull in the latest changes in your main branch. "
    printf "Code checking will not work properly with an outdated 'main' branch.\n"
    echo ""
fi
