#!/usr/bin/env bash

export PHPCS_PATH=''

type -P phpcs

if [[ $(type -p phpcs) ]]; then
    printf '\033[32m%s\033[0m \033[32m%s\033[0m' 'Using globally installed PHP-CS from' "$(type -p phpcs)"
    PHPCS_PATH=phpcs
elif [ -f ./vendor/bin/phpcs ]; then
    printf '\033[32m %s \033[0m \n' 'Using locally installed PHP-CS'
    PHPCS_PATH=./vendor/bin/phpcs
else
    printf '\033[32m %s \033[0m \n' 'No PHP-CS installation found. Exiting...'
fi

if [[ ! PHPCS_PATH -eq "" ]]; then
    git diff --name-only --diff-filter=ACMRT | xargs $PHPCS_PATH
    git ls-files --others --exclude-standard | xargs $PHPCS_PATH
fi
