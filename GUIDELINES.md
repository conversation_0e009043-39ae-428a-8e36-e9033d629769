# 🏗️ Backend Development Guidelines

> **Comprehensive guidelines for <PERSON><PERSON> backend development at our tech company**
> These guidelines ensure code quality, maintainability, and team consistency.

---

## 📋 Table of Contents

- [🎯 Core Principles](#-core-principles)
- [📁 Project Structure](#-project-structure)
- [💻 Code Standards](#-code-standards)
- [🗄️ Database Guidelines](#️-database-guidelines)
- [🔐 Security Best Practices](#-security-best-practices)
- [🧪 Testing Requirements](#-testing-requirements)
- [📚 API Development](#-api-development)
- [⚡ Performance Guidelines](#-performance-guidelines)
- [📝 Documentation Standards](#-documentation-standards)
- [🔄 Git Workflow](#-git-workflow)
- [🚨 Error Handling](#-error-handling)
- [📊 Logging & Monitoring](#-logging--monitoring)
- [🔧 Environment Management](#-environment-management)
- [📋 Code Review Process](#-code-review-process)

---

## 🎯 Core Principles

### 1. **DRY (Don't Repeat Yourself)**
- Extract common functionality into reusable components
- Use Laravel's built-in features before creating custom solutions
- Create helper functions for repeated logic

### 2. **KISS (Keep It Simple, Stupid)**
- Write clear, readable code
- Avoid over-engineering solutions
- Use Laravel conventions and patterns

---

## 📁 Project Structure

### **Mandatory Directory Structure**
```
app/
├── Classes/              # Single-purpose action classes
├── DTOs/                 # Data Transfer Objects
├── Enums/                # PHP 8.1+ Enums
├── Events/               # Laravel Events
├── Exceptions/           # Custom exceptions
├── Http/
│   ├── Controllers/      # Keep thin, delegate to services
│   ├── Middleware/       # Custom middleware
│   ├── Requests/         # Form request validation
│   └── Resources/        # API resources
├── Jobs/                 # Queue jobs
├── Listeners/            # Event listeners
├── Models/               # Eloquent models
├── Policies/             # Authorization policies
├── Providers/            # Service providers
├── Rules/                # Custom validation rules
├── Services/             # Business logic layer
└── Traits/               # Reusable traits
```

### **Service Layer Architecture**
```php
// ✅ Good: Thin controller with service injection
class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    public function store(CreateUserRequest $request): JsonResponse
    {
        $user = $this->userService->createUser($request->validated());
        return new UserResource($user);
    }
}

// ❌ Bad: Fat controller with business logic
class UserController extends Controller
{
    public function store(Request $request)
    {
        // Validation, business logic, database operations all mixed
    }
}
```

---

## 💻 Code Standards

### **PSR Standards Compliance**
- **PSR-1**: Basic coding standard
- **PSR-12**: Extended coding style guide
- **PSR-4**: Autoloading standard

### **Naming Conventions**

| Type | Convention | Example |
|------|------------|---------|
| **Classes** | PascalCase | `UserService`, `PaymentController` |
| **Methods** | camelCase | `getUserById()`, `processPayment()` |
| **Variables** | camelCase | `$userId`, `$paymentAmount` |
| **Constants** | UPPER_SNAKE_CASE | `MAX_RETRY_ATTEMPTS` |
| **Database Tables** | snake_case (plural) | `users`, `order_items` |
| **Database Columns** | snake_case | `created_at`, `user_id` |

### **Method and Class Guidelines**

```php
// ✅ Good: Clear, single responsibility
class UserService
{
    public function createUser(array $userData): User
    {
        return DB::transaction(function () use ($userData) {
            $user = User::create($userData);
            $this->sendWelcomeEmail($user);
            return $user;
        });
    }

    private function sendWelcomeEmail(User $user): void
    {
        Mail::to($user)->send(new WelcomeEmail($user));
    }
}

// ❌ Bad: Multiple responsibilities, unclear naming
class UserStuff
{
    public function doUserThings($data)
    {
        // Creates user, sends email, logs activity, updates cache...
    }
}
```

### **Type Declarations**
- **Always use type hints** for parameters and return types
- **Use nullable types** when appropriate (`?string`, `?User`)
- **Use union types** for PHP 8.0+ (`string|int`)

```php
// ✅ Good: Proper type declarations
public function processPayment(
    User $user,
    float $amount,
    ?string $currency = null
): PaymentResult {
    // Implementation
}

// ❌ Bad: No type declarations
public function processPayment($user, $amount, $currency = null)
{
    // Implementation
}
```

---

## 🗄️ Database Guidelines

### **Migration Best Practices**

```php
// ✅ Good: Descriptive migration with proper constraints
Schema::create('order_items', function (Blueprint $table) {
    $table->id();
    $table->foreignId('order_id')->constrained()->cascadeOnDelete();
    $table->foreignId('product_id')->constrained();
    $table->integer('quantity')->unsigned();
    $table->decimal('unit_price', 15, 2);
    $table->decimal('total_price', 15, 2);
    $table->timestamps();

    $table->index(['order_id', 'product_id']);
});

// ❌ Bad: Missing constraints and indexes
Schema::create('order_items', function (Blueprint $table) {
    $table->id();
    $table->integer('order_id');
    $table->integer('product_id');
    $table->integer('quantity');
    $table->float('price');
    $table->timestamps();
});
```

### **Model Guidelines**

```php
// ✅ Good: Comprehensive model setup
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    // Scopes
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    // Accessors & Mutators
    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => ucfirst($value),
            set: fn (string $value) => strtolower($value),
        );
    }
}
```

### **Query Optimization**

```php
// ✅ Good: Eager loading to prevent N+1
$users = User::with(['orders.items', 'profile'])
    ->where('is_active', true)
    ->get();

// ✅ Good: Using specific columns
$users = User::select(['id', 'name', 'email'])
    ->active()
    ->get();

// ❌ Bad: N+1 query problem
$users = User::all();
foreach ($users as $user) {
    echo $user->orders->count(); // N+1 queries
}
```

---

## 🔐 Security Best Practices

### **Input Validation**
- **Always validate input** using Form Requests
- **Use Laravel's built-in validation rules**
- **Create custom rules** for complex validation

```php
// ✅ Good: Comprehensive form request
class CreateUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', User::class);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'role' => ['required', 'in:admin,user,moderator'],
        ];
    }

    public function messages(): array
    {
        return [
            'email.unique' => 'This email address is already registered.',
        ];
    }
}
```

### **Authentication & Authorization**

```php
// ✅ Good: Policy-based authorization
class PostPolicy
{
    public function update(User $user, Post $post): bool
    {
        return $user->id === $post->user_id || $user->hasRole('admin');
    }
}

// Controller usage
public function update(UpdatePostRequest $request, Post $post): JsonResponse
{
    $this->authorize('update', $post);

    $updatedPost = $this->postService->update($post, $request->validated());

    return new PostResource($updatedPost);
}
```

### **SQL Injection Prevention**
- **Always use Eloquent ORM** or Query Builder
- **Never concatenate user input** into raw SQL
- **Use parameter binding** for raw queries

```php
// ✅ Good: Using Eloquent
$users = User::where('email', $email)->get();

// ✅ Good: Parameter binding for raw queries
$users = DB::select('SELECT * FROM users WHERE email = ?', [$email]);

// ❌ Bad: SQL injection vulnerability
$users = DB::select("SELECT * FROM users WHERE email = '$email'");
```

---

## 🧪 Testing Requirements

### **Testing Standards**
- **Minimum 80% code coverage** for new features
- **Write tests before implementing** (TDD approach preferred)
- **Test all public methods** and critical business logic

### **Test Structure**

```php
// ✅ Good: Comprehensive test structure
class UserServiceTest extends TestCase
{
    use RefreshDatabase;

    private UserService $userService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userService = app(UserService::class);
    }

    /** @test */
    public function it_creates_user_with_valid_data(): void
    {
        // Arrange
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        // Act
        $user = $this->userService->createUser($userData);

        // Assert
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('John Doe', $user->name);
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    /** @test */
    public function it_throws_exception_when_email_already_exists(): void
    {
        // Arrange
        User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        // Act & Assert
        $this->expectException(ValidationException::class);
        $this->userService->createUser($userData);
    }
}
```

### **Test Categories**
- **Unit Tests**: Test individual methods and classes
- **Feature Tests**: Test complete features and user flows
- **Integration Tests**: Test component interactions
- **API Tests**: Test API endpoints and responses

---

## 📚 API Development

### **RESTful API Standards**

| HTTP Method | Endpoint | Purpose |
|-------------|----------|---------|
| GET | `/api/users` | List all users |
| GET | `/api/users/{id}` | Get specific user |
| POST | `/api/users` | Create new user |
| PUT/PATCH | `/api/users/{id}` | Update user |
| DELETE | `/api/users/{id}` | Delete user |

### **Response Structure**

```php
// ✅ Good: Consistent API response structure
class ApiResponse
{
    public static function success($data = null, string $message = 'Success', int $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
        ], $status);
    }

    public static function error(string $message, $errors = null, int $status = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $status);
    }
}

// Usage in controller
public function store(CreateUserRequest $request): JsonResponse
{
    try {
        $user = $this->userService->createUser($request->validated());
        return ApiResponse::success(new UserResource($user), 'User created successfully', 201);
    } catch (Exception $e) {
        return ApiResponse::error('Failed to create user', null, 500);
    }
}
```

### **API Resource Usage**

```php
// ✅ Good: Comprehensive API resource
class UserResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),

            // Conditional fields
            'orders' => OrderResource::collection($this->whenLoaded('orders')),
            'is_admin' => $this->when($this->hasRole('admin'), true),

            // Computed fields
            'full_name' => $this->first_name . ' ' . $this->last_name,
        ];
    }
}
```

---

## ⚡ Performance Guidelines

### **Database Optimization**
- **Use database indexes** for frequently queried columns
- **Implement query caching** for expensive operations
- **Use database transactions** for data consistency
- **Optimize N+1 queries** with eager loading

### **Caching Strategy**

```php
// ✅ Good: Proper cache implementation
class UserService
{
    public function getUserById(int $id): ?User
    {
        return Cache::remember(
            "user.{$id}",
            now()->addHours(1),
            fn () => User::with(['profile', 'roles'])->find($id)
        );
    }

    public function updateUser(User $user, array $data): User
    {
        $user->update($data);

        // Clear related cache
        Cache::forget("user.{$user->id}");
        Cache::tags(['users'])->flush();

        return $user->fresh();
    }
}
```

### **Queue Usage**
- **Use queues for time-consuming tasks**
- **Implement proper job failure handling**
- **Use job batching for related tasks**

```php
// ✅ Good: Proper queue job implementation
class SendWelcomeEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $maxExceptions = 2;

    public function __construct(
        private User $user
    ) {}

    public function handle(): void
    {
        Mail::to($this->user)->send(new WelcomeEmail($this->user));
    }

    public function failed(Throwable $exception): void
    {
        Log::error('Failed to send welcome email', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
```

---

## 📝 Documentation Standards

### **Code Documentation**

```php
/**
 * Create a new user account with the provided data.
 *
 * This method handles user creation including password hashing,
 * email verification setup, and welcome email dispatch.
 *
 * @param array $userData The user data containing name, email, and password
 * @return User The created user instance
 * @throws ValidationException When user data is invalid
 * @throws UserCreationException When user creation fails
 */
public function createUser(array $userData): User
{
    // Implementation
}
```

### **API Documentation**
- **Use Laravel Scramble** or similar tools for automatic API documentation
- **Document all endpoints** with request/response examples
- **Include authentication requirements**
- **Provide error response examples**

---

## 🔄 Git Workflow

### **Branch Naming Convention**
- `feature/user-authentication`
- `bugfix/payment-validation-error`
- `hotfix/security-vulnerability`
- `refactor/user-service-optimization`

### **Commit Message Format**
```
type(scope): brief description

Detailed explanation of what was changed and why.

- List any breaking changes
- Reference issue numbers (#123)
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### **Pull Request Requirements**
- ✅ Code coverage maintained
- ✅ Code review approved by @maarufadam
- ✅ Documentation updated
- ✅ No merge conflicts

---

## 🚨 Error Handling

### **Exception Hierarchy**

```php
// ✅ Good: Custom exception hierarchy
namespace App\Exceptions;

class BusinessLogicException extends Exception
{
    public function __construct(
        string $message,
        public readonly array $context = [],
        int $code = 0,
        ?Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}

class UserNotFoundException extends BusinessLogicException
{
    public function __construct(int $userId)
    {
        parent::__construct(
            "User with ID {$userId} not found",
            ['user_id' => $userId],
            404
        );
    }
}
```

### **Global Exception Handler**

```php
// app/Exceptions/Handler.php
public function register(): void
{
    $this->reportable(function (Throwable $e) {
        if ($e instanceof BusinessLogicException) {
            Log::warning('Business logic exception', [
                'message' => $e->getMessage(),
                'context' => $e->context,
                'trace' => $e->getTraceAsString(),
            ]);
        }
    });

    $this->renderable(function (BusinessLogicException $e, Request $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ], $e->getCode() ?: 400);
        }
    });
}
```

### **Service Layer Error Handling**

```php
// ✅ Good: Proper error handling in services
class PaymentService
{
    public function processPayment(User $user, float $amount): PaymentResult
    {
        try {
            $this->validatePaymentAmount($amount);
            $this->checkUserBalance($user, $amount);

            return DB::transaction(function () use ($user, $amount) {
                $payment = $this->createPayment($user, $amount);
                $this->updateUserBalance($user, $amount);

                return new PaymentResult($payment);
            });

        } catch (InsufficientFundsException $e) {
            Log::info('Payment failed due to insufficient funds', [
                'user_id' => $user->id,
                'amount' => $amount,
            ]);
            throw $e;

        } catch (Exception $e) {
            Log::error('Unexpected payment processing error', [
                'user_id' => $user->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);
            throw new PaymentProcessingException('Payment processing failed', 0, $e);
        }
    }
}
```

---

## 📊 Logging & Monitoring

### **Logging Standards**

```php
// ✅ Good: Structured logging with context
class UserService
{
    public function createUser(array $userData): User
    {
        Log::info('Creating new user', [
            'email' => $userData['email'],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        try {
            $user = User::create($userData);

            Log::info('User created successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);

            return $user;

        } catch (Exception $e) {
            Log::error('Failed to create user', [
                'email' => $userData['email'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
}
```

### **Performance Monitoring**

```php
// ✅ Good: Performance monitoring middleware
class PerformanceMonitoringMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        $response = $next($request);

        $executionTime = microtime(true) - $startTime;
        $memoryUsage = memory_get_usage(true) - $startMemory;

        if ($executionTime > 1.0) { // Log slow requests
            Log::warning('Slow request detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage,
                'user_id' => auth()->id(),
            ]);
        }

        return $response;
    }
}
```

### **Health Checks**

```php
// ✅ Good: Comprehensive health check endpoint
class HealthController extends Controller
{
    public function check(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'queue' => $this->checkQueue(),
            'storage' => $this->checkStorage(),
        ];

        $allHealthy = collect($checks)->every(fn($check) => $check['status'] === 'ok');

        return response()->json([
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
        ], $allHealthy ? 200 : 503);
    }

    private function checkDatabase(): array
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'ok', 'message' => 'Database connection successful'];
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed'];
        }
    }
}
```

---

## 🔧 Environment Management

### **Environment Configuration**

```php
// config/app.php - Environment-specific settings
return [
    'name' => env('APP_NAME', 'Laravel'),
    'env' => env('APP_ENV', 'production'),
    'debug' => (bool) env('APP_DEBUG', false),

    // Custom environment configurations
    'features' => [
        'new_payment_system' => env('FEATURE_NEW_PAYMENT_SYSTEM', false),
        'advanced_analytics' => env('FEATURE_ADVANCED_ANALYTICS', false),
    ],

    'limits' => [
        'max_upload_size' => env('MAX_UPLOAD_SIZE', 10485760), // 10MB
        'rate_limit_per_minute' => env('RATE_LIMIT_PER_MINUTE', 60),
    ],
];
```

### **Feature Flags**

```php
// ✅ Good: Feature flag implementation
class FeatureFlag
{
    public static function isEnabled(string $feature, ?User $user = null): bool
    {
        // Check environment configuration
        $envKey = 'FEATURE_' . strtoupper($feature);
        if (!env($envKey, false)) {
            return false;
        }

        // Check user-specific flags
        if ($user && $user->hasFeatureFlag($feature)) {
            return true;
        }

        // Check percentage rollout
        return self::isInPercentageRollout($feature, $user);
    }

    private static function isInPercentageRollout(string $feature, ?User $user): bool
    {
        $percentage = config("features.rollout.{$feature}", 0);
        if ($percentage === 0) return false;
        if ($percentage === 100) return true;

        $hash = md5($feature . ($user?->id ?? 'anonymous'));
        return (hexdec(substr($hash, 0, 8)) % 100) < $percentage;
    }
}

// Usage in controllers
public function store(Request $request): JsonResponse
{
    if (FeatureFlag::isEnabled('new_payment_system', auth()->user())) {
        return $this->newPaymentFlow($request);
    }

    return $this->legacyPaymentFlow($request);
}
```

### **Environment Validation**

```php
// app/Providers/AppServiceProvider.php
public function boot(): void
{
    if (app()->environment('production')) {
        $this->validateProductionEnvironment();
    }
}

private function validateProductionEnvironment(): void
{
    $requiredEnvVars = [
        'APP_KEY',
        'DB_PASSWORD',
        'MAIL_MAILER',
        'CACHE_DRIVER',
        'QUEUE_CONNECTION',
    ];

    foreach ($requiredEnvVars as $var) {
        if (empty(env($var))) {
            throw new RuntimeException("Required environment variable {$var} is not set");
        }
    }
}
```

---

## 📋 Code Review Process

### **Pull Request Template**

```markdown
## 🎯 Purpose
Brief description of what this PR accomplishes.

## 🔄 Changes Made
- [ ] Feature implementation
- [ ] Bug fix
- [ ] Refactoring
- [ ] Documentation update

## 🧪 Testing
- [ ] Unit tests added/updated
- [ ] Feature tests added/updated
- [ ] Manual testing completed
- [ ] Edge cases covered

## 📊 Performance Impact
- [ ] No performance impact
- [ ] Performance improved
- [ ] Performance impact analyzed and acceptable

## 🔐 Security Considerations
- [ ] No security implications
- [ ] Security review completed
- [ ] Input validation implemented
- [ ] Authorization checks added

## 📝 Documentation
- [ ] Code comments added
- [ ] API documentation updated
- [ ] README updated if needed

## 🔗 Related Issues
Closes #123
Related to #456
```

### **Review Checklist**

#### **Code Quality**
- [ ] Code follows established patterns and conventions
- [ ] Methods are focused and have single responsibility
- [ ] Variable and method names are descriptive
- [ ] Code is DRY (Don't Repeat Yourself)
- [ ] Complex logic is properly commented

#### **Architecture & Design**
- [ ] Changes follow SOLID principles
- [ ] Proper separation of concerns
- [ ] Dependencies are properly injected
- [ ] Service layer is used for business logic
- [ ] Models are not overloaded with business logic

#### **Security**
- [ ] Input validation is comprehensive
- [ ] SQL injection prevention measures in place
- [ ] Authorization checks implemented
- [ ] Sensitive data is not logged or exposed
- [ ] CSRF protection where applicable

#### **Performance**
- [ ] Database queries are optimized
- [ ] N+1 query problems avoided
- [ ] Appropriate caching implemented
- [ ] Memory usage is reasonable
- [ ] No unnecessary loops or operations

#### **Testing**
- [ ] Unit tests cover new functionality
- [ ] Edge cases are tested
- [ ] Error scenarios are tested
- [ ] Tests are readable and maintainable
- [ ] Test coverage meets minimum requirements

#### **Documentation**
- [ ] Public methods have proper docblocks
- [ ] Complex algorithms are explained
- [ ] API changes are documented
- [ ] Breaking changes are highlighted

### **Review Process Flow**

1. **Author Self-Review** (Before requesting review)
   - Run all tests locally
   - Check code against style guidelines
   - Verify documentation is complete
   - Test manually in development environment

2. **Peer Review** (2+ reviewers required)
   - Technical review for code quality
   - Architecture and design review
   - Security and performance review
   - Test coverage verification

3. **Senior Developer Review** (For complex changes)
   - Architecture decisions validation
   - Performance impact assessment
   - Security implications review
   - Long-term maintainability evaluation

4. **Final Approval & Merge**
   - All automated checks pass
   - All review comments addressed
   - Documentation updated
   - Ready for deployment

### **Common Review Comments & Solutions**

| Issue | Solution |
|-------|----------|
| "This method is too long" | Extract smaller methods with single responsibilities |
| "Missing error handling" | Add try-catch blocks and proper exception handling |
| "N+1 query detected" | Use eager loading with `with()` method |
| "Magic numbers used" | Extract to named constants or configuration |
| "Missing tests" | Add unit/feature tests for new functionality |
| "Security vulnerability" | Implement proper validation and authorization |

---

## 🎯 Enforcement & Compliance

### **Automated Checks**
- **PHP CS Fixer**: Automatic code style fixing
- **PHPStan**: Static analysis for type safety
- **Psalm**: Additional static analysis
- **PHP Insights**: Code quality metrics
- **Larastan**: Laravel-specific static analysis

### **Pre-commit Hooks**
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Run PHP CS Fixer
vendor/bin/php-cs-fixer fix --dry-run --diff
if [ $? -ne 0 ]; then
    echo "Code style issues found. Run 'vendor/bin/php-cs-fixer fix' to fix them."
    exit 1
fi

# Run PHPStan
vendor/bin/phpstan analyse
if [ $? -ne 0 ]; then
    echo "Static analysis issues found."
    exit 1
fi

# Run tests
vendor/bin/phpunit
if [ $? -ne 0 ]; then
    echo "Tests failed."
    exit 1
fi
```

---

## 📚 Resources & Training

### **Required Reading**
- [Laravel Documentation](https://laravel.com/docs)
- [PHP: The Right Way](https://phptherightway.com/)
- [Clean Code by Robert Martin](https://www.amazon.com/Clean-Code-Handbook-Software-Craftsmanship/dp/0132350882)
- [Refactoring by Martin Fowler](https://refactoring.com/)

### **Recommended Tools**
- **IDE**: PhpStorm or VS Code with PHP extensions
- **Database**: TablePlus or Sequel Pro
- **API Testing**: Postman or Insomnia

### **Team Training Schedule**
- **Monthly**: Architecture review sessions
- **Quarterly**: Security best practices workshop
- **Bi-annually**: Laravel version upgrade training
- **Annually**: Full-stack performance optimization

---

<div align="center">

**🏆 Excellence Through Consistency**

*These guidelines are living documents - they evolve as we learn and grow as a team.*

**Questions? Suggestions? Open an issue or start a discussion!**

---

**Last Updated**: 27th August 2025
**Version**: 1.0
**Next Review**: 27th September 2025

</div>
