<?php

use App\Exceptions\Auth\UnauthorizedAuthDomainException;
use App\Http\Middleware\CheckTokenExpiration;
use App\Http\Middleware\Permission\PermissionMiddleware;
use App\Http\Middleware\Permission\RoleMiddleware;
use App\Http\Middleware\Permission\RoleOrPermissionMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Laravel\Sanctum\Http\Middleware\CheckAbilities;
use Laravel\Sanctum\Http\Middleware\CheckForAnyAbility;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        /// Permission
        $middleware->alias([
            'role' => RoleMiddleware::class,
            'permission' => PermissionMiddleware::class,
            'checkTokenExpiration' => CheckTokenExpiration::class,
            // 'abilities' => CheckAbilities::class,
            // 'ability' => CheckForAnyAbility::class,
            // 'role_or_permission' => \App\Middlewares\Permission\RoleOrPermissionMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (UnauthorizedAuthDomainException $e, Request $request) {
            return response()->view('shared.auth.unauthorized-domain', status: 500);
        });
    })->create();
