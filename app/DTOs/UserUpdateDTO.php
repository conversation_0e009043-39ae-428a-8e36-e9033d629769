<?php

declare(strict_types=1);

namespace App\DTOs;

class UserUpdateDTO
{
    public function __construct(
        public readonly ?string $first_name = null,
        public readonly ?string $last_name = null,
        public readonly ?string $phone = null,
        public readonly ?string $company_name = null,
        public readonly ?string $rc_number = null,
        public readonly ?string $tax_id = null,
        public readonly ?string $address = null,
        public readonly ?string $city = null,
        public readonly ?string $state = null,
        public readonly ?string $country = null,
        public readonly ?string $postal_code = null,
        public readonly ?string $website = null,
        public readonly ?string $position = null,
        public readonly ?string $business_type = null,
        public readonly ?int $years_in_business = null,
        public readonly ?float $annual_revenue = null,
        public readonly ?int $number_of_employees = null,
        public readonly ?string $business_description = null,
        public readonly ?string $profile_picture = null,
        public readonly ?string $status = null,
        public readonly ?string $admin_notes = null,
        public readonly ?string $password = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            first_name: $data['first_name'] ?? null,
            last_name: $data['last_name'] ?? null,
            phone: $data['phone'] ?? null,
            company_name: $data['company_name'] ?? null,
            rc_number: $data['rc_number'] ?? null,
            tax_id: $data['tax_id'] ?? null,
            address: $data['address'] ?? null,
            city: $data['city'] ?? null,
            state: $data['state'] ?? null,
            country: $data['country'] ?? null,
            postal_code: $data['postal_code'] ?? null,
            website: $data['website'] ?? null,
            position: $data['position'] ?? null,
            business_type: $data['business_type'] ?? null,
            years_in_business: isset($data['years_in_business']) ? (int) $data['years_in_business'] : null,
            annual_revenue: isset($data['annual_revenue']) ? (float) $data['annual_revenue'] : null,
            number_of_employees: isset($data['number_of_employees']) ? (int) $data['number_of_employees'] : null,
            business_description: $data['business_description'] ?? null,
            profile_picture: $data['profile_picture'] ?? null,
            status: $data['status'] ?? null,
            admin_notes: $data['admin_notes'] ?? null,
            password: $data['password'] ?? null,
        );
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->first_name !== null) {
            $data['first_name'] = $this->first_name;
        }
        if ($this->last_name !== null) {
            $data['last_name'] = $this->last_name;
        }
        if ($this->phone !== null) {
            $data['phone'] = $this->phone;
        }
        if ($this->company_name !== null) {
            $data['company_name'] = $this->company_name;
        }
        if ($this->rc_number !== null) {
            $data['rc_number'] = $this->rc_number;
        }
        if ($this->tax_id !== null) {
            $data['tax_id'] = $this->tax_id;
        }
        if ($this->address !== null) {
            $data['address'] = $this->address;
        }
        if ($this->city !== null) {
            $data['city'] = $this->city;
        }
        if ($this->state !== null) {
            $data['state'] = $this->state;
        }
        if ($this->country !== null) {
            $data['country'] = $this->country;
        }
        if ($this->postal_code !== null) {
            $data['postal_code'] = $this->postal_code;
        }
        if ($this->website !== null) {
            $data['website'] = $this->website;
        }
        if ($this->position !== null) {
            $data['position'] = $this->position;
        }
        if ($this->business_type !== null) {
            $data['business_type'] = $this->business_type;
        }
        if ($this->years_in_business !== null) {
            $data['years_in_business'] = $this->years_in_business;
        }
        if ($this->annual_revenue !== null) {
            $data['annual_revenue'] = $this->annual_revenue;
        }
        if ($this->number_of_employees !== null) {
            $data['number_of_employees'] = $this->number_of_employees;
        }
        if ($this->business_description !== null) {
            $data['business_description'] = $this->business_description;
        }
        if ($this->profile_picture !== null) {
            $data['profile_picture'] = $this->profile_picture;
        }
        if ($this->status !== null) {
            $data['status'] = $this->status;
        }
        if ($this->admin_notes !== null) {
            $data['admin_notes'] = $this->admin_notes;
        }
        if ($this->password !== null) {
            $data['password'] = $this->password;
        }

        return $data;
    }
}
