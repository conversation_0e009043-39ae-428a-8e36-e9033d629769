<?php

namespace App\DTOs;

class UserDTO
{
    public function __construct(
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $identifier,
        public readonly string $phone,
        public readonly string $email,
        public readonly string $password,
    ) {
        //
    }

    public static function fromRequest($request): self
    {
        return new self(
            $request->first_name,
            $request->last_name,
            $request->identifier,
            $request->phone,
            $request->email,
            $request->password,
        );
    }

    public function toArray(): array
    {
        return [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'identifier' => $this->identifier,
            'phone' => $this->phone,
            'email' => $this->email,
            'password' => $this->password,
        ];
    }
}
