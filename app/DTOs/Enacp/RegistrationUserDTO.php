<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

class RegistrationUserDTO
{
    public function __construct(
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $email,
        public readonly string $phone,
        public readonly string $password,
        public readonly ?string $profile_picture = null,
        public readonly string $status = 'pending',
        public readonly string $role = 'user',
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            first_name: $data['first_name'],
            last_name: $data['last_name'],
            email: $data['email'],
            phone: $data['phone'],
            password: $data['password'], // Should be hashed before passing to DTO
            profile_picture: $data['profile_picture'] ?? null,
            status: $data['status'] ?? 'pending',
            role: $data['role'] ?? 'user',
        );
    }

    public function toArray(): array
    {
        return [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'password' => $this->password,
            'profile_picture' => $this->profile_picture,
            'status' => $this->status,
            'role' => $this->role,
        ];
    }
}
