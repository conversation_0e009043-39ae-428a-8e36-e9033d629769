<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class CertificateDTO
{
    public function __construct(
        public readonly int $accreditation_application_id,
        public readonly string $certificate_number,
        public readonly ?string $qr_code_path = null,
        public readonly ?string $certificate_path = null,
        public readonly ?string $certificate_image_path = null,
        public readonly ?string $issued_date = null,
        public readonly ?string $expiry_date = null,
        public readonly string $status = 'active',
        public readonly array $certificate_data = [],
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            accreditation_application_id: $request->integer('accreditation_application_id'),
            certificate_number: $request->input('certificate_number'),
            qr_code_path: $request->input('qr_code_path'),
            certificate_path: $request->input('certificate_path'),
            certificate_image_path: $request->input('certificate_image_path'),
            issued_date: $request->input('issued_date'),
            expiry_date: $request->input('expiry_date'),
            status: $request->input('status', 'active'),
            certificate_data: $request->input('certificate_data', []),
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            accreditation_application_id: $data['accreditation_application_id'],
            certificate_number: $data['certificate_number'],
            qr_code_path: $data['qr_code_path'] ?? null,
            certificate_path: $data['certificate_path'] ?? null,
            certificate_image_path: $data['certificate_image_path'] ?? null,
            issued_date: $data['issued_date'] ?? null,
            expiry_date: $data['expiry_date'] ?? null,
            status: $data['status'] ?? 'active',
            certificate_data: $data['certificate_data'] ?? [],
        );
    }

    public function toArray(): array
    {
        return [
            'accreditation_application_id' => $this->accreditation_application_id,
            'certificate_number' => $this->certificate_number,
            'qr_code_path' => $this->qr_code_path,
            'certificate_path' => $this->certificate_path,
            'certificate_image_path' => $this->certificate_image_path,
            'issued_date' => $this->issued_date,
            'expiry_date' => $this->expiry_date,
            'status' => $this->status,
            'certificate_data' => $this->certificate_data,
        ];
    }
}
