<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class DocumentUpdateDTO
{
    public function __construct(
        public readonly ?int $accreditation_application_id = null,
        public readonly ?string $document_type = null,
        public readonly ?string $file_name = null,
        public readonly ?string $file_path = null,
        public readonly ?int $file_size = null,
        public readonly ?string $mime_type = null,
        public readonly ?string $verification_status = null,
        public readonly ?bool $is_required = null,
        public readonly ?string $expiry_date = null,
        public readonly ?string $verification_notes = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        $file = $request->file('document');

        return new self(
            accreditation_application_id: $request->has('accreditation_application_id')
                ? $request->integer('accreditation_application_id') : null,
            document_type: $request->has('document_type') ? $request->input('document_type') : null,
            file_name: $file ? $file->getClientOriginalName() :
                ($request->has('file_name') ? $request->input('file_name') : null),
            file_path: $request->has('file_path') ? $request->input('file_path') : null,
            file_size: $file ? $file->getSize() :
                ($request->has('file_size') ? $request->integer('file_size') : null),
            mime_type: $file ? $file->getMimeType() :
                ($request->has('mime_type') ? $request->input('mime_type') : null),
            verification_status: $request->has('verification_status') ? $request->input('verification_status') : null,
            is_required: $request->has('is_required') ? $request->boolean('is_required') : null,
            expiry_date: $request->has('expiry_date') ? $request->input('expiry_date') : null,
            verification_notes: $request->has('verification_notes') ? $request->input('verification_notes') : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            accreditation_application_id: $data['accreditation_application_id'] ?? null,
            document_type: $data['document_type'] ?? null,
            file_name: $data['file_name'] ?? null,
            file_path: $data['file_path'] ?? null,
            file_size: $data['file_size'] ?? null,
            mime_type: $data['mime_type'] ?? null,
            verification_status: $data['verification_status'] ?? null,
            is_required: $data['is_required'] ?? null,
            expiry_date: $data['expiry_date'] ?? null,
            verification_notes: $data['verification_notes'] ?? null,
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'accreditation_application_id' => $this->accreditation_application_id,
            'document_type' => $this->document_type,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'mime_type' => $this->mime_type,
            'verification_status' => $this->verification_status,
            'is_required' => $this->is_required,
            'expiry_date' => $this->expiry_date,
            'verification_notes' => $this->verification_notes,
        ], fn($value) => $value !== null);
    }
}
