<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

class BusinessProfileDTO
{
    public function __construct(
        public readonly string $company_name,
        public readonly ?string $rc_number,
        public readonly ?string $tax_id,
        public readonly string $address,
        public readonly string $city,
        public readonly string $state,
        public readonly string $country,
        public readonly ?string $postal_code,
        public readonly ?string $website,
        public readonly string $position,
        public readonly string $business_type,
        public readonly int $years_in_business,
        public readonly float $annual_revenue,
        public readonly int $number_of_employees,
        public readonly string $business_description,
        public readonly int $category_subtype_id,
        public readonly ?string $payment_reference,
        public readonly ?int $created_by = null,
        public readonly ?int $modified_by = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            company_name: $data['company_name'],
            rc_number: $data['rc_number'] ?? null,
            tax_id: $data['tax_id'] ?? null,
            address: $data['address'],
            city: $data['city'],
            state: $data['state'],
            country: $data['country'],
            postal_code: $data['postal_code'] ?? null,
            website: $data['website'] ?? null,
            position: $data['position'],
            business_type: $data['business_type'],
            years_in_business: (int) $data['years_in_business'],
            annual_revenue: (float) $data['annual_revenue'],
            number_of_employees: (int) $data['number_of_employees'],
            business_description: $data['business_description'],
            category_subtype_id: (int) $data['category_subtype_id'],
            payment_reference: $data['payment_reference'] ?? null,
            created_by: isset($data['created_by']) ? (int) $data['created_by'] : null,
            modified_by: isset($data['modified_by']) ? (int) $data['modified_by'] : null,
        );
    }

    public function toArray(): array
    {
        return [
            'company_name' => $this->company_name,
            'rc_number' => $this->rc_number,
            'tax_id' => $this->tax_id,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'website' => $this->website,
            'position' => $this->position,
            'business_type' => $this->business_type,
            'years_in_business' => $this->years_in_business,
            'annual_revenue' => $this->annual_revenue,
            'number_of_employees' => $this->number_of_employees,
            'business_description' => $this->business_description,
            'category_subtype_id' => $this->category_subtype_id,
            'payment_reference' => $this->payment_reference,
            'created_by' => $this->created_by,
            'modified_by' => $this->modified_by,
        ];
    }
}
