<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class CategorySubtypeDTO
{
    public function __construct(
        public readonly int $stakeholder_category_id,
        public readonly string $name,
        public readonly string $slug,
        public readonly float $accreditation_fee,
        public readonly float $renewal_fee,
        public readonly string $currency = 'NGN',
        public readonly bool $is_active = true,
        public readonly int $sort_order = 0,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            stakeholder_category_id: $request->integer('stakeholder_category_id'),
            name: $request->input('name'),
            slug: $request->input('slug') ?? \Str::slug($request->input('name')),
            accreditation_fee: $request->float('accreditation_fee'),
            renewal_fee: $request->float('renewal_fee'),
            currency: $request->input('currency', 'NGN'),
            is_active: $request->boolean('is_active', true),
            sort_order: $request->integer('sort_order', 0),
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            stakeholder_category_id: $data['stakeholder_category_id'],
            name: $data['name'],
            slug: $data['slug'] ?? \Str::slug($data['name']),
            accreditation_fee: $data['accreditation_fee'],
            renewal_fee: $data['renewal_fee'],
            currency: $data['currency'] ?? 'NGN',
            is_active: $data['is_active'] ?? true,
            sort_order: $data['sort_order'] ?? 0,
        );
    }

    public function toArray(): array
    {
        return [
            'stakeholder_category_id' => $this->stakeholder_category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'accreditation_fee' => $this->accreditation_fee,
            'renewal_fee' => $this->renewal_fee,
            'currency' => $this->currency,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ];
    }
}
