<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class StakeholderCategoryUpdateDTO
{
    public function __construct(
        public readonly ?string $name = null,
        public readonly ?string $slug = null,
        public readonly ?string $description = null,
        public readonly ?string $icon = null,
        public readonly ?bool $is_active = null,
        public readonly ?int $sort_order = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            name: $request->has('name') ? $request->input('name') : null,
            slug: $request->has('slug') ? $request->input('slug')
                : ($request->has('name') ? \Str::slug($request->input('name')) : null),
            description: $request->has('description') ? $request->input('description') : null,
            icon: $request->has('icon') ? $request->input('icon') : null,
            is_active: $request->has('is_active') ? $request->boolean('is_active') : null,
            sort_order: $request->has('sort_order') ? $request->integer('sort_order') : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'] ?? null,
            slug: $data['slug'] ?? (isset($data['name']) ? \Str::slug($data['name']) : null),
            description: $data['description'] ?? null,
            icon: $data['icon'] ?? null,
            is_active: $data['is_active'] ?? null,
            sort_order: $data['sort_order'] ?? null,
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'icon' => $this->icon,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ], fn($value) => $value !== null);
    }
}
