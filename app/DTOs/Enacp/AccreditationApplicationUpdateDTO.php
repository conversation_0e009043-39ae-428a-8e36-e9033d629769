<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class AccreditationApplicationUpdateDTO
{
    public function __construct(
        public readonly ?int $user_id = null,
        public readonly ?int $stakeholder_category_id = null,
        public readonly ?int $category_subtype_id = null,
        public readonly ?string $application_number = null,
        public readonly ?string $status = null,
        public readonly ?float $fee_paid = null,
        public readonly ?string $payment_status = null,
        public readonly ?float $fee_amount = null,
        public readonly ?array $application_data = null,
        public readonly ?string $submitted_at = null,
        public readonly ?string $admin_notes = null,
        public readonly ?string $rejection_reason = null,
        public readonly ?string $payment_reference = null,
        public readonly ?int $reviewed_by = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            user_id: $request->has('user_id') ? $request->integer('user_id') : null,
            stakeholder_category_id: $request->has('stakeholder_category_id')
                ? $request->integer('stakeholder_category_id') : null,
            category_subtype_id: $request->has('category_subtype_id') ? $request->integer('category_subtype_id') : null,
            application_number: $request->has('application_number') ? $request->input('application_number') : null,
            status: $request->has('status') ? $request->input('status') : null,
            fee_paid: $request->has('fee_paid') ? $request->float('fee_paid') : null,
            payment_status: $request->has('payment_status') ? $request->input('payment_status') : null,
            fee_amount: $request->has('fee_amount') ? $request->float('fee_amount') : null,
            application_data: $request->has('application_data') ? $request->input('application_data') : null,
            submitted_at: $request->has('submitted_at') ? $request->input('submitted_at') : null,
            admin_notes: $request->has('admin_notes') ? $request->input('admin_notes') : null,
            rejection_reason: $request->has('rejection_reason') ? $request->input('rejection_reason') : null,
            payment_reference: $request->has('payment_reference') ? $request->input('payment_reference') : null,
            reviewed_by: $request->has('reviewed_by') ? $request->integer('reviewed_by') : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            user_id: $data['user_id'] ?? null,
            stakeholder_category_id: $data['stakeholder_category_id'] ?? null,
            category_subtype_id: $data['category_subtype_id'] ?? null,
            application_number: $data['application_number'] ?? null,
            status: $data['status'] ?? null,
            fee_paid: $data['fee_paid'] ?? null,
            payment_status: $data['payment_status'] ?? null,
            fee_amount: $data['fee_amount'] ?? null,
            application_data: $data['application_data'] ?? null,
            submitted_at: $data['submitted_at'] ?? null,
            admin_notes: $data['admin_notes'] ?? null,
            rejection_reason: $data['rejection_reason'] ?? null,
            payment_reference: $data['payment_reference'] ?? null,
            reviewed_by: $data['reviewed_by'] ?? null,
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'user_id' => $this->user_id,
            'stakeholder_category_id' => $this->stakeholder_category_id,
            'category_subtype_id' => $this->category_subtype_id,
            'application_number' => $this->application_number,
            'status' => $this->status,
            'fee_paid' => $this->fee_paid,
            'payment_status' => $this->payment_status,
            'fee_amount' => $this->fee_amount,
            'application_data' => $this->application_data,
            'submitted_at' => $this->submitted_at,
            'admin_notes' => $this->admin_notes,
            'rejection_reason' => $this->rejection_reason,
            'payment_reference' => $this->payment_reference,
            'reviewed_by' => $this->reviewed_by,
        ], fn($value) => $value !== null);
    }
}
