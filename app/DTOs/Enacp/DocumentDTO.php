<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class DocumentDTO
{
    public function __construct(
        public readonly int $accreditation_application_id,
        public readonly string $document_type,
        public readonly string $file_name,
        public readonly string $file_path,
        public readonly int $file_size,
        public readonly string $mime_type,
        public readonly string $verification_status = 'pending',
        public readonly bool $is_required = true,
        public readonly ?string $expiry_date = null,
        public readonly ?string $verification_notes = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        $file = $request->file('document');

        return new self(
            accreditation_application_id: $request->integer('accreditation_application_id'),
            document_type: $request->input('document_type'),
            file_name: $file ? $file->getClientOriginalName() : $request->input('file_name'),
            file_path: $request->input('file_path', ''), // Will be set after upload
            file_size: $file ? $file->getSize() : $request->integer('file_size', 0),
            mime_type: $file ? $file->getMimeType() : $request->input('mime_type', ''),
            verification_status: $request->input('verification_status', 'pending'),
            is_required: $request->boolean('is_required', true),
            expiry_date: $request->input('expiry_date'),
            verification_notes: $request->input('verification_notes'),
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            accreditation_application_id: $data['accreditation_application_id'],
            document_type: $data['document_type'],
            file_name: $data['file_name'],
            file_path: $data['file_path'],
            file_size: $data['file_size'],
            mime_type: $data['mime_type'],
            verification_status: $data['verification_status'] ?? 'pending',
            is_required: $data['is_required'] ?? true,
            expiry_date: $data['expiry_date'] ?? null,
            verification_notes: $data['verification_notes'] ?? null,
        );
    }

    public static function fromUploadedFile(
        int $applicationId,
        string $documentType,
        UploadedFile $file,
        string $filePath,
        bool $isRequired = true,
        ?string $expiryDate = null
    ): self {
        return new self(
            accreditation_application_id: $applicationId,
            document_type: $documentType,
            file_name: $file->getClientOriginalName(),
            file_path: $filePath,
            file_size: $file->getSize(),
            mime_type: $file->getMimeType(),
            verification_status: 'pending',
            is_required: $isRequired,
            expiry_date: $expiryDate,
            verification_notes: null,
        );
    }

    public function toArray(): array
    {
        return [
            'accreditation_application_id' => $this->accreditation_application_id,
            'document_type' => $this->document_type,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'mime_type' => $this->mime_type,
            'verification_status' => $this->verification_status,
            'is_required' => $this->is_required,
            'expiry_date' => $this->expiry_date,
            'verification_notes' => $this->verification_notes,
        ];
    }
}
