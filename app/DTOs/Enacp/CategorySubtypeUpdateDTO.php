<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class CategorySubtypeUpdateDTO
{
    public function __construct(
        public readonly ?int $stakeholder_category_id = null,
        public readonly ?string $name = null,
        public readonly ?string $slug = null,
        public readonly ?float $accreditation_fee = null,
        public readonly ?float $renewal_fee = null,
        public readonly ?string $currency = null,
        public readonly ?bool $is_active = null,
        public readonly ?int $sort_order = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            stakeholder_category_id: $request->has('stakeholder_category_id')
                ? $request->integer('stakeholder_category_id') : null,
            name: $request->has('name') ? $request->input('name') : null,
            slug: $request->has('slug') ? $request->input('slug')
                : ($request->has('name') ? \Str::slug($request->input('name')) : null),
            accreditation_fee: $request->has('accreditation_fee') ? $request->float('accreditation_fee') : null,
            renewal_fee: $request->has('renewal_fee') ? $request->float('renewal_fee') : null,
            currency: $request->has('currency') ? $request->input('currency') : null,
            is_active: $request->has('is_active') ? $request->boolean('is_active') : null,
            sort_order: $request->has('sort_order') ? $request->integer('sort_order') : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            stakeholder_category_id: $data['stakeholder_category_id'] ?? null,
            name: $data['name'] ?? null,
            slug: $data['slug'] ?? (isset($data['name']) ? \Str::slug($data['name']) : null),
            accreditation_fee: $data['accreditation_fee'] ?? null,
            renewal_fee: $data['renewal_fee'] ?? null,
            currency: $data['currency'] ?? null,
            is_active: $data['is_active'] ?? null,
            sort_order: $data['sort_order'] ?? null,
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'stakeholder_category_id' => $this->stakeholder_category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'accreditation_fee' => $this->accreditation_fee,
            'renewal_fee' => $this->renewal_fee,
            'currency' => $this->currency,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ], fn($value) => $value !== null);
    }
}
