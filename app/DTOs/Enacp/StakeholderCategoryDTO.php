<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class StakeholderCategoryDTO
{
    public function __construct(
        public readonly string $name,
        public readonly string $slug,
        public readonly string $description,
        public readonly string $icon,
        public readonly bool $is_active = true,
        public readonly int $sort_order = 0,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            name: $request->input('name'),
            slug: $request->input('slug') ?? \Str::slug($request->input('name')),
            description: $request->input('description'),
            icon: $request->input('icon'),
            is_active: $request->boolean('is_active', true),
            sort_order: $request->integer('sort_order', 0),
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            name: $data['name'],
            slug: $data['slug'] ?? \Str::slug($data['name']),
            description: $data['description'],
            icon: $data['icon'],
            is_active: $data['is_active'] ?? true,
            sort_order: $data['sort_order'] ?? 0,
        );
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'icon' => $this->icon,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ];
    }
}
