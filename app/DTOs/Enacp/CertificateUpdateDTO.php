<?php

declare(strict_types=1);

namespace App\DTOs\Enacp;

use Illuminate\Http\Request;

class CertificateUpdateDTO
{
    public function __construct(
        public readonly ?int $accreditation_application_id = null,
        public readonly ?string $certificate_number = null,
        public readonly ?string $qr_code_path = null,
        public readonly ?string $certificate_path = null,
        public readonly ?string $certificate_image_path = null,
        public readonly ?string $issued_date = null,
        public readonly ?string $expiry_date = null,
        public readonly ?string $status = null,
        public readonly ?array $certificate_data = null,
    ) {
    }

    public static function fromRequest(Request $request): self
    {
        return new self(
            accreditation_application_id: $request->has('accreditation_application_id')
                ? $request->integer('accreditation_application_id') : null,
            certificate_number: $request->has('certificate_number') ? $request->input('certificate_number') : null,
            qr_code_path: $request->has('qr_code_path') ? $request->input('qr_code_path') : null,
            certificate_path: $request->has('certificate_path') ? $request->input('certificate_path') : null,
            certificate_image_path: $request->has('certificate_image_path')
                ? $request->input('certificate_image_path') : null,
            issued_date: $request->has('issued_date') ? $request->input('issued_date') : null,
            expiry_date: $request->has('expiry_date') ? $request->input('expiry_date') : null,
            status: $request->has('status') ? $request->input('status') : null,
            certificate_data: $request->has('certificate_data') ? $request->input('certificate_data') : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new self(
            accreditation_application_id: $data['accreditation_application_id'] ?? null,
            certificate_number: $data['certificate_number'] ?? null,
            qr_code_path: $data['qr_code_path'] ?? null,
            certificate_path: $data['certificate_path'] ?? null,
            certificate_image_path: $data['certificate_image_path'] ?? null,
            issued_date: $data['issued_date'] ?? null,
            expiry_date: $data['expiry_date'] ?? null,
            status: $data['status'] ?? null,
            certificate_data: $data['certificate_data'] ?? null,
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'accreditation_application_id' => $this->accreditation_application_id,
            'certificate_number' => $this->certificate_number,
            'qr_code_path' => $this->qr_code_path,
            'certificate_path' => $this->certificate_path,
            'certificate_image_path' => $this->certificate_image_path,
            'issued_date' => $this->issued_date,
            'expiry_date' => $this->expiry_date,
            'status' => $this->status,
            'certificate_data' => $this->certificate_data,
        ], fn($value) => $value !== null);
    }
}
