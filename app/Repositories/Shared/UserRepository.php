<?php

namespace App\Repositories\Shared;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class UserRepository
{
    public function __construct(protected User $user)
    {
        //
    }


    public function create(array $data): User
    {
        return $this->user->create($data);
    }

    public function update(User $user, array $data): User
    {
        $user->update($data);

        return $user->refresh();
    }

    public function delete(User $user): bool
    {
        return $user->delete();
    }

    public function getAllUsers(): Collection
    {
        return $this->user->with('roles', 'roles.permissions')->get();
    }

    public function getUserById(int $id): User
    {
        return $this->user->find($id);
    }

    public function getUserByEmail(string $email): User
    {
        return $this->user->where('email', $email)->first();
    }

    public function getUserByPhoneNo(string $phone): User
    {
        return $this->user->where('phone', $phone)->first();
    }

    public function getUserByIdentifier(string $identifier): User
    {
        return $this->user->where('identifier', $identifier)->first();
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        return $this->user->where('email', $email)->exists();
    }

    /**
     * Find user by ID
     */
    public function findById(int $id): ?User
    {
        return $this->user->find($id);
    }

    /**
     * Find user by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?User
    {
        $query = $this->user->where('id', $id);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }

    /**
     * Get all users with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        $query = $this->user->query();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }
}
