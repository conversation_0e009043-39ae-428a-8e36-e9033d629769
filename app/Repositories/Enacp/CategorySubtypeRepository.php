<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\CategorySubtype;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategorySubtypeRepository
{
    public function __construct(protected CategorySubtype $model)
    {
    }

    /**
     * Create a new category subtype
     */
    public function create(array $data): CategorySubtype
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing category subtype
     */
    public function update(CategorySubtype $subtype, array $data): CategorySubtype
    {
        $subtype->update($data);
        return $subtype->refresh();
    }

    /**
     * Delete a category subtype
     */
    public function delete(CategorySubtype $subtype): bool
    {
        return $subtype->delete();
    }

    /**
     * Find a category subtype by ID
     */
    public function findById(int $id): ?CategorySubtype
    {
        return $this->model->find($id);
    }

    /**
     * Find a category subtype by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?CategorySubtype
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id);
    }

    /**
     * Find a category subtype by slug
     */
    public function findBySlug(string $slug): ?CategorySubtype
    {
        return $this->model->where('slug', $slug)->first();
    }

    /**
     * Get all category subtypes
     */
    public function getAll(): Collection
    {
        return $this->model->all();
    }

    /**
     * Get all category subtypes with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get paginated category subtypes
     */
    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->paginate($perPage);
    }

    /**
     * Get active category subtypes
     */
    public function getActive(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get active category subtypes with relationships
     */
    public function getActiveWithRelations(array $relations = []): Collection
    {
        $query = $this->model->active();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get subtypes by stakeholder category ID
     */
    public function getByCategoryId(int $categoryId): Collection
    {
        return $this->model->where('stakeholder_category_id', $categoryId)->get();
    }

    /**
     * Get active subtypes by stakeholder category ID
     */
    public function getActiveByCategoryId(int $categoryId): Collection
    {
        return $this->model->active()->where('stakeholder_category_id', $categoryId)->get();
    }

    /**
     * Get subtypes ordered by sort order
     */
    public function getOrderedBySort(): Collection
    {
        return $this->model->orderBy('sort_order')->get();
    }

    /**
     * Get active subtypes ordered by sort order
     */
    public function getActiveOrderedBySort(): Collection
    {
        return $this->model->active()->orderBy('sort_order')->get();
    }

    /**
     * Search subtypes by name
     */
    public function searchByName(string $name): Collection
    {
        return $this->model->where('name', 'like', "%{$name}%")->get();
    }

    /**
     * Get subtypes by currency
     */
    public function getByCurrency(string $currency): Collection
    {
        return $this->model->where('currency', $currency)->get();
    }

    /**
     * Get subtypes with fee range
     */
    public function getByFeeRange(float $minFee, float $maxFee, string $feeType = 'accreditation_fee'): Collection
    {
        return $this->model->whereBetween($feeType, [$minFee, $maxFee])->get();
    }

    /**
     * Check if a subtype name exists within a category
     */
    public function nameExistsInCategory(string $name, int $categoryId, ?int $excludeId = null): bool
    {
        $query = $this->model->where('name', $name)->where('stakeholder_category_id', $categoryId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if a subtype slug exists
     */
    public function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get the next sort order value for a category
     */
    public function getNextSortOrderForCategory(int $categoryId): int
    {
        return $this->model->where('stakeholder_category_id', $categoryId)->max('sort_order') + 1;
    }

    /**
     * Reorder subtypes within a category
     */
    public function reorderInCategory(int $categoryId, array $subtypeIds): bool
    {
        foreach ($subtypeIds as $index => $subtypeId) {
            $this->model->where('id', $subtypeId)
                ->where('stakeholder_category_id', $categoryId)
                ->update(['sort_order' => $index + 1]);
        }

        return true;
    }

    /**
     * Get subtypes with application counts
     */
    public function getWithApplicationCounts(): Collection
    {
        return $this->model->withCount('applications')->get();
    }

    /**
     * Find subtype by ID and category
     */
    public function findByIdAndCategory(int $id, int $categoryId): ?CategorySubtype
    {
        return $this->model->where('id', $id)
            ->where('stakeholder_category_id', $categoryId)
            ->first();
    }

    /**
     * Check if subtype has applications
     */
    public function hasApplications(int $subtypeId): bool
    {
        return $this->model->find($subtypeId)?->applications()->exists() ?? false;
    }
}
