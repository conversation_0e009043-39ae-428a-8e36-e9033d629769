<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\Certificate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class CertificateRepository
{
    public function __construct(protected Certificate $model)
    {
    }

    public function create(array $data): Certificate
    {
        return $this->model->create($data);
    }

    public function update(Certificate $certificate, array $data): Certificate
    {
        $certificate->update($data);
        return $certificate->refresh();
    }

    public function delete(Certificate $certificate): bool
    {
        return $certificate->delete();
    }

    public function findById(int $id): ?Certificate
    {
        return $this->model->find($id);
    }

    public function findByIdWithRelations(int $id, array $relations = []): ?Certificate
    {
        $query = $this->model->newQuery();
        if (!empty($relations)) {
            $query->with($relations);
        }
        return $query->find($id);
    }

    public function findByCertificateNumber(string $certificateNumber): ?Certificate
    {
        return $this->model->where('certificate_number', $certificateNumber)->first();
    }

    public function getAll(): Collection
    {
        return $this->model->all();
    }

    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        if (!empty($relations)) {
            $query->with($relations);
        }
        return $query->orderBy('issued_date', 'desc')->paginate($perPage);
    }

    public function getByApplicationId(int $applicationId): Collection
    {
        return $this->model->where('accreditation_application_id', $applicationId)->get();
    }

    public function getByStatus(string $status): Collection
    {
        return $this->model->where('status', $status)->orderBy('issued_date', 'desc')->get();
    }

    public function getActive(): Collection
    {
        return $this->model->where('status', 'active')->orderBy('issued_date', 'desc')->get();
    }

    public function getSuspended(): Collection
    {
        return $this->model->where('status', 'suspended')->orderBy('updated_at', 'desc')->get();
    }

    public function getRevoked(): Collection
    {
        return $this->model->where('status', 'revoked')->orderBy('updated_at', 'desc')->get();
    }

    public function getExpired(): Collection
    {
        return $this->model->where('expiry_date', '<', Carbon::now())
            ->orderBy('expiry_date', 'desc')->get();
    }

    public function getExpiringSoon(int $days = 30): Collection
    {
        $expiryDate = Carbon::now()->addDays($days);
        return $this->model->where('expiry_date', '<=', $expiryDate)
            ->where('expiry_date', '>', Carbon::now())
            ->where('status', 'active')
            ->orderBy('expiry_date', 'asc')->get();
    }

    public function getIssuedBetweenDates(string $startDate, string $endDate): Collection
    {
        return $this->model->whereBetween('issued_date', [$startDate, $endDate])
            ->orderBy('issued_date', 'desc')->get();
    }

    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (isset($criteria['certificate_number'])) {
            $query->where('certificate_number', 'like', "%{$criteria['certificate_number']}%");
        }

        if (isset($criteria['status'])) {
            $query->where('status', $criteria['status']);
        }

        if (isset($criteria['application_id'])) {
            $query->where('accreditation_application_id', $criteria['application_id']);
        }

        if (isset($criteria['issued_from'])) {
            $query->where('issued_date', '>=', $criteria['issued_from']);
        }

        if (isset($criteria['issued_to'])) {
            $query->where('issued_date', '<=', $criteria['issued_to']);
        }

        return $query->orderBy('issued_date', 'desc')->paginate($perPage);
    }

    public function certificateNumberExists(string $certificateNumber, ?int $excludeId = null): bool
    {
        $query = $this->model->where('certificate_number', $certificateNumber);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        return $query->exists();
    }

    public function getNextCertificateNumber(): string
    {
        $year = date('Y');
        $prefix = 'CERT/' . $year . '/';

        $lastCertificate = $this->model->where('certificate_number', 'like', $prefix . '%')
            ->orderBy('certificate_number', 'desc')->first();

        if (!$lastCertificate) {
            return $prefix . '0001';
        }

        $lastNumber = (int) substr($lastCertificate->certificate_number, -4);
        $lastNumber += 1;
        $nextNumber = str_pad((string) $lastNumber, 4, '0', STR_PAD_LEFT);

        return $prefix . $nextNumber;
    }

    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'active' => $this->model->where('status', 'active')->count(),
            'suspended' => $this->model->where('status', 'suspended')->count(),
            'revoked' => $this->model->where('status', 'revoked')->count(),
            'expired' => $this->model->where('expiry_date', '<', Carbon::now())->count(),
            'expiring_soon' => $this->model->where('expiry_date', '<=', Carbon::now()->addDays(30))
                ->where('expiry_date', '>', Carbon::now())->count(),
        ];
    }

    public function getByUserId(int $userId): Collection
    {
        return $this->model->whereHas('accreditationApplication', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->orderBy('issued_date', 'desc')->get();
    }

    /**
     * Find certificate by application ID
     */
    public function findByApplicationId(int $applicationId): ?Certificate
    {
        return $this->model->where('accreditation_application_id', $applicationId)->first();
    }

    /**
     * Find certificate by certificate number with relationships
     */
    public function findByCertificateNumberWithRelations(string $certificateNumber, array $relations = []): ?Certificate
    {
        $query = $this->model->where('certificate_number', $certificateNumber);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }
}
