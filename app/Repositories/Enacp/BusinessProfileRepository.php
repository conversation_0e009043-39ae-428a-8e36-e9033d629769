<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\BusinessProfile;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class BusinessProfileRepository
{
    public function __construct(protected BusinessProfile $model)
    {
    }

    /**
     * Create a new business profile
     */
    public function create(array $data): BusinessProfile
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing business profile
     */
    public function update(BusinessProfile $businessProfile, array $data): BusinessProfile
    {
        $businessProfile->update($data);
        return $businessProfile->refresh();
    }

    /**
     * Delete a business profile
     */
    public function delete(BusinessProfile $businessProfile): bool
    {
        return $businessProfile->delete();
    }

    /**
     * Find a business profile by ID
     */
    public function findById(int $id): ?BusinessProfile
    {
        return $this->model->find($id);
    }

    /**
     * Find a business profile by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?BusinessProfile
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id);
    }

    /**
     * Get all business profiles
     */
    public function getAll(): Collection
    {
        return $this->model->all();
    }

    /**
     * Get all business profiles with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get paginated business profiles
     */
    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->paginate($perPage);
    }

    /**
     * Find business profiles by company name
     */
    public function findByCompanyName(string $companyName): Collection
    {
        return $this->model->where('company_name', 'like', "%{$companyName}%")->get();
    }

    /**
     * Find business profiles by business type
     */
    public function findByBusinessType(string $businessType): Collection
    {
        return $this->model->byBusinessType($businessType)->get();
    }

    /**
     * Find business profiles by country
     */
    public function findByCountry(string $country): Collection
    {
        return $this->model->byCountry($country)->get();
    }

    /**
     * Find business profiles by category subtype
     */
    public function findByCategorySubtype(int $categorySubtypeId): Collection
    {
        return $this->model->where('category_subtype_id', $categorySubtypeId)->get();
    }

    /**
     * Find business profile by payment reference
     */
    public function findByPaymentReference(string $paymentReference): ?BusinessProfile
    {
        return $this->model->where('payment_reference', $paymentReference)->first();
    }

    /**
     * Search business profiles by various criteria
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (isset($criteria['company_name'])) {
            $query->where('company_name', 'like', "%{$criteria['company_name']}%");
        }

        if (isset($criteria['business_type'])) {
            $query->where('business_type', $criteria['business_type']);
        }

        if (isset($criteria['country'])) {
            $query->where('country', $criteria['country']);
        }

        if (isset($criteria['state'])) {
            $query->where('state', $criteria['state']);
        }

        if (isset($criteria['category_subtype_id'])) {
            $query->where('category_subtype_id', $criteria['category_subtype_id']);
        }

        if (isset($criteria['min_revenue'])) {
            $query->where('annual_revenue', '>=', $criteria['min_revenue']);
        }

        if (isset($criteria['max_revenue'])) {
            $query->where('annual_revenue', '<=', $criteria['max_revenue']);
        }

        if (isset($criteria['min_employees'])) {
            $query->where('number_of_employees', '>=', $criteria['min_employees']);
        }

        if (isset($criteria['max_employees'])) {
            $query->where('number_of_employees', '<=', $criteria['max_employees']);
        }

        return $query->paginate($perPage);
    }

    /**
     * Get active business profiles
     */
    public function getActive(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get business profiles with annual revenue above a certain amount
     */
    public function getByMinimumRevenue(float $minimumRevenue): Collection
    {
        return $this->model->where('annual_revenue', '>=', $minimumRevenue)->get();
    }

    /**
     * Get business profiles with employee count within a range
     */
    public function getByEmployeeRange(int $minEmployees, int $maxEmployees): Collection
    {
        return $this->model->whereBetween('number_of_employees', [$minEmployees, $maxEmployees])->get();
    }
}
