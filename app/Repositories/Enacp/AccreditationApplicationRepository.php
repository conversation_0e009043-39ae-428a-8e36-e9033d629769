<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\AccreditationApplication;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class AccreditationApplicationRepository
{
    public function __construct(protected AccreditationApplication $model)
    {
    }

    /**
     * Create a new accreditation application
     */
    public function create(array $data): AccreditationApplication
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing accreditation application
     */
    public function update(AccreditationApplication $application, array $data): AccreditationApplication
    {
        $application->update($data);
        return $application->refresh();
    }

    /**
     * Delete an accreditation application
     */
    public function delete(AccreditationApplication $application): bool
    {
        return $application->delete();
    }

    /**
     * Find an accreditation application by ID
     */
    public function findById(int $id): ?AccreditationApplication
    {
        return $this->model->find($id);
    }

    /**
     * Find an accreditation application by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?AccreditationApplication
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id);
    }

    /**
     * Find an accreditation application by application number
     */
    public function findByApplicationNumber(string $applicationNumber): ?AccreditationApplication
    {
        return $this->model->where('application_number', $applicationNumber)->first();
    }

    /**
     * Get all accreditation applications
     */
    public function getAll(): Collection
    {
        return $this->model->all();
    }

    /**
     * Get all accreditation applications with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get paginated accreditation applications
     */
    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get applications by user ID
     */
    public function getByUserId(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by user ID with relationships
     */
    public function getByUserIdWithRelations(int $userId, array $relations = []): Collection
    {
        $query = $this->model->where('user_id', $userId);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->model->where('status', $status)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by multiple statuses
     */
    public function getByStatuses(array $statuses): Collection
    {
        return $this->model->whereIn('status', $statuses)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by stakeholder category
     */
    public function getByCategoryId(int $categoryId): Collection
    {
        return $this->model->where('stakeholder_category_id', $categoryId)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by category subtype
     */
    public function getBySubtypeId(int $subtypeId): Collection
    {
        return $this->model->where('category_subtype_id', $subtypeId)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications by payment status
     */
    public function getByPaymentStatus(string $paymentStatus): Collection
    {
        return $this->model->where('payment_status', $paymentStatus)->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get applications submitted within date range
     */
    public function getSubmittedBetweenDates(string $startDate, string $endDate): Collection
    {
        return $this->model->whereBetween('submitted_at', [$startDate, $endDate])
            ->orderBy('submitted_at', 'desc')
            ->get();
    }

    /**
     * Get applications reviewed by user
     */
    public function getReviewedByUser(int $userId): Collection
    {
        return $this->model->where('reviewed_by', $userId)
            ->whereNotNull('reviewed_at')
            ->orderBy('reviewed_at', 'desc')
            ->get();
    }

    /**
     * Search applications by various criteria
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (isset($criteria['application_number'])) {
            $query->where('application_number', 'like', "%{$criteria['application_number']}%");
        }

        if (isset($criteria['status'])) {
            $query->where('status', $criteria['status']);
        }

        if (isset($criteria['payment_status'])) {
            $query->where('payment_status', $criteria['payment_status']);
        }

        if (isset($criteria['user_id'])) {
            $query->where('user_id', $criteria['user_id']);
        }

        if (isset($criteria['category_id'])) {
            $query->where('stakeholder_category_id', $criteria['category_id']);
        }

        if (isset($criteria['subtype_id'])) {
            $query->where('category_subtype_id', $criteria['subtype_id']);
        }

        if (isset($criteria['submitted_from'])) {
            $query->where('submitted_at', '>=', $criteria['submitted_from']);
        }

        if (isset($criteria['submitted_to'])) {
            $query->where('submitted_at', '<=', $criteria['submitted_to']);
        }

        if (isset($criteria['min_fee'])) {
            $query->where('fee_amount', '>=', $criteria['min_fee']);
        }

        if (isset($criteria['max_fee'])) {
            $query->where('fee_amount', '<=', $criteria['max_fee']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get applications pending review
     */
    public function getPendingReview(): Collection
    {
        return $this->model->where('status', 'submitted')
            ->orWhere('status', 'under_review')
            ->orderBy('submitted_at', 'asc')
            ->get();
    }

    /**
     * Get approved applications
     */
    public function getApproved(): Collection
    {
        return $this->model->where('status', 'approved')->orderBy('reviewed_at', 'desc')->get();
    }

    /**
     * Get rejected applications
     */
    public function getRejected(): Collection
    {
        return $this->model->where('status', 'rejected')->orderBy('reviewed_at', 'desc')->get();
    }

    /**
     * Get applications with expired certificates
     */
    public function getWithExpiredCertificates(): Collection
    {
        return $this->model->whereHas('certificate', function ($query) {
            $query->where('expiry_date', '<', Carbon::now());
        })->get();
    }

    /**
     * Get applications with certificates expiring soon
     */
    public function getWithCertificatesExpiringSoon(int $days = 30): Collection
    {
        $expiryDate = Carbon::now()->addDays($days);

        return $this->model->whereHas('certificate', function ($query) use ($expiryDate) {
            $query->where('expiry_date', '<=', $expiryDate)
                  ->where('expiry_date', '>', Carbon::now());
        })->get();
    }

    /**
     * Check if application number exists
     */
    public function applicationNumberExists(string $applicationNumber, ?int $excludeId = null): bool
    {
        $query = $this->model->where('application_number', $applicationNumber);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get next application number
     */
    public function getNextApplicationNumber(): string
    {
        $year = date('Y');
        $prefix = 'ENACP/' . $year . '/';

        $lastApplication = $this->model->where('application_number', 'like', $prefix . '%')
            ->orderBy('application_number', 'desc')
            ->first();

        if (!$lastApplication) {
            return $prefix . '0001';
        }

        $lastNumber = (int) substr($lastApplication->application_number, -4);
        $lastNumber += 1;
        $nextNumber = str_pad((string) $lastNumber, 4, '0', STR_PAD_LEFT);

        return $prefix . $nextNumber;
    }

    /**
     * Get applications statistics
     */
    public function getStatistics(): array
    {
        $total = $this->model->count();
        $draft = $this->model->where('status', 'draft')->count();
        $submitted = $this->model->where('status', 'submitted')->count();
        $underReview = $this->model->where('status', 'under_review')->count();
        $approved = $this->model->where('status', 'approved')->count();
        $rejected = $this->model->where('status', 'rejected')->count();
        $expired = $this->model->where('status', 'expired')->count();

        return [
            'total' => $total,
            'draft' => $draft,
            'submitted' => $submitted,
            'under_review' => $underReview,
            'approved' => $approved,
            'rejected' => $rejected,
            'expired' => $expired,
            'pending_review' => $submitted + $underReview,
        ];
    }

    /**
     * Get applications with document counts
     */
    public function getWithDocumentCounts(): Collection
    {
        return $this->model->withCount('documents')->get();
    }

    /**
     * Get applications by fee amount range
     */
    public function getByFeeRange(float $minFee, float $maxFee): Collection
    {
        return $this->model->whereBetween('fee_amount', [$minFee, $maxFee])
            ->orderBy('fee_amount', 'desc')
            ->get();
    }

    /**
     * Find application by ID and user ID
     */
    public function findByIdAndUserId(int $id, int $userId, array $relations = []): ?AccreditationApplication
    {
        $query = $this->model->where('id', $id)->where('user_id', $userId);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }

    /**
     * Find draft application by ID and user ID
     */
    public function findDraftByIdAndUserId(int $id, int $userId, array $relations = []): ?AccreditationApplication
    {
        $query = $this->model->where('id', $id)
            ->where('user_id', $userId)
            ->where('status', 'draft');

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }

    /**
     * Check if user has pending application for category
     */
    public function userHasPendingApplicationForCategory(int $userId, int $categoryId): bool
    {
        return $this->model->where('user_id', $userId)
            ->where('stakeholder_category_id', $categoryId)
            ->whereIn('status', ['draft', 'submitted', 'under_review'])
            ->exists();
    }

    /**
     * Find application by application number with relationships
     */
    public function findByApplicationNumberWithRelations(
        string $applicationNumber,
        array $relations = []
    ): ?AccreditationApplication {
        $query = $this->model->where('application_number', $applicationNumber);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }

    /**
     * Find approved application by ID with relationships
     */
    public function findApprovedByIdWithRelations(int $id, array $relations = []): ?AccreditationApplication
    {
        $query = $this->model->where('id', $id)->where('status', 'approved');

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }

    /**
     * Find user's draft application by ID
     */
    public function findUserDraftApplication(int $userId, int $applicationId): ?AccreditationApplication
    {
        return $this->model->where('id', $applicationId)
            ->where('user_id', $userId)
            ->where('status', 'draft')
            ->first();
    }
}
