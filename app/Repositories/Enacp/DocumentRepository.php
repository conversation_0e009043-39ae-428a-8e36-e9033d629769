<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\Document;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class DocumentRepository
{
    public function __construct(protected Document $model)
    {
    }

    public function create(array $data): Document
    {
        return $this->model->create($data);
    }

    public function update(Document $document, array $data): Document
    {
        $document->update($data);
        return $document->refresh();
    }

    public function delete(Document $document): bool
    {
        return $document->delete();
    }

    public function findById(int $id): ?Document
    {
        return $this->model->find($id);
    }

    public function findByIdWithRelations(int $id, array $relations = []): ?Document
    {
        $query = $this->model->newQuery();
        if (!empty($relations)) {
            $query->with($relations);
        }
        return $query->find($id);
    }

    public function getAll(): Collection
    {
        return $this->model->all();
    }

    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        if (!empty($relations)) {
            $query->with($relations);
        }
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getByApplicationId(int $applicationId): Collection
    {
        return $this->model->where('accreditation_application_id', $applicationId)
            ->orderBy('created_at', 'desc')->get();
    }

    public function getByDocumentType(string $documentType): Collection
    {
        return $this->model->where('document_type', $documentType)
            ->orderBy('created_at', 'desc')->get();
    }

    public function getByVerificationStatus(string $status): Collection
    {
        return $this->model->where('verification_status', $status)
            ->orderBy('created_at', 'desc')->get();
    }

    public function getRequired(): Collection
    {
        return $this->model->required()->orderBy('created_at', 'desc')->get();
    }

    public function getOptional(): Collection
    {
        return $this->model->where('is_required', false)
            ->orderBy('created_at', 'desc')->get();
    }

    public function getExpiringSoon(int $days = 30): Collection
    {
        $expiryDate = Carbon::now()->addDays($days);
        return $this->model->whereNotNull('expiry_date')
            ->where('expiry_date', '<=', $expiryDate)
            ->where('expiry_date', '>', Carbon::now())
            ->orderBy('expiry_date', 'asc')->get();
    }

    public function getExpired(): Collection
    {
        return $this->model->whereNotNull('expiry_date')
            ->where('expiry_date', '<', Carbon::now())
            ->orderBy('expiry_date', 'desc')->get();
    }

    public function getByMimeType(string $mimeType): Collection
    {
        return $this->model->where('mime_type', $mimeType)
            ->orderBy('created_at', 'desc')->get();
    }

    public function getBySizeRange(int $minSize, int $maxSize): Collection
    {
        return $this->model->whereBetween('file_size', [$minSize, $maxSize])
            ->orderBy('file_size', 'desc')->get();
    }

    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (isset($criteria['application_id'])) {
            $query->where('accreditation_application_id', $criteria['application_id']);
        }

        if (isset($criteria['document_type'])) {
            $query->where('document_type', $criteria['document_type']);
        }

        if (isset($criteria['verification_status'])) {
            $query->where('verification_status', $criteria['verification_status']);
        }

        if (isset($criteria['is_required'])) {
            $query->where('is_required', $criteria['is_required']);
        }

        if (isset($criteria['file_name'])) {
            $query->where('file_name', 'like', "%{$criteria['file_name']}%");
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'pending' => $this->model->where('verification_status', 'pending')->count(),
            'verified' => $this->model->where('verification_status', 'verified')->count(),
            'rejected' => $this->model->where('verification_status', 'rejected')->count(),
            'required' => $this->model->where('is_required', true)->count(),
            'optional' => $this->model->where('is_required', false)->count(),
            'expired' => $this->model->whereNotNull('expiry_date')
                ->where('expiry_date', '<', Carbon::now())->count(),
        ];
    }

    public function getPendingVerification(): Collection
    {
        return $this->model->where('verification_status', 'pending')
            ->orderBy('created_at', 'asc')->get();
    }

    public function getVerified(): Collection
    {
        return $this->model->where('verification_status', 'verified')
            ->orderBy('updated_at', 'desc')->get();
    }

    public function getRejected(): Collection
    {
        return $this->model->where('verification_status', 'rejected')
            ->orderBy('updated_at', 'desc')->get();
    }

    /**
     * Find document by application ID and document type
     */
    public function findByApplicationAndType(int $applicationId, string $documentType): ?Document
    {
        return $this->model->where('accreditation_application_id', $applicationId)
            ->where('document_type', $documentType)
            ->first();
    }

    /**
     * Find user document by ID
     */
    public function findUserDocument(int $userId, int $documentId): ?Document
    {
        return $this->model->where('id', $documentId)
            ->whereHas('accreditationApplication', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->first();
    }
}
