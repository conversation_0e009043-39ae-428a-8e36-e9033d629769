<?php

declare(strict_types=1);

namespace App\Repositories\Enacp;

use App\Models\Enacp\StakeholderCategory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class StakeholderCategoryRepository
{
    public function __construct(protected StakeholderCategory $model)
    {
    }

    /**
     * Create a new stakeholder category
     */
    public function create(array $data): StakeholderCategory
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing stakeholder category
     */
    public function update(StakeholderCategory $category, array $data): StakeholderCategory
    {
        $category->update($data);
        return $category->refresh();
    }

    /**
     * Delete a stakeholder category
     */
    public function delete(StakeholderCategory $category): bool
    {
        return $category->delete();
    }

    /**
     * Find a stakeholder category by ID
     */
    public function findById(int $id): ?StakeholderCategory
    {
        return $this->model->find($id);
    }

    /**
     * Find a stakeholder category by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?StakeholderCategory
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id);
    }

    /**
     * Find a stakeholder category by slug
     */
    public function findBySlug(string $slug): ?StakeholderCategory
    {
        return $this->model->where('slug', $slug)->first();
    }

    /**
     * Get all stakeholder categories
     */
    public function getAll(): Collection
    {
        return $this->model->all();
    }

    /**
     * Get all stakeholder categories with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get paginated stakeholder categories
     */
    public function getPaginated(int $perPage = 15, array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->paginate($perPage);
    }

    /**
     * Get active stakeholder categories
     */
    public function getActive(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get active stakeholder categories with relationships
     */
    public function getActiveWithRelations(array $relations = []): Collection
    {
        $query = $this->model->active();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get();
    }

    /**
     * Get stakeholder categories ordered by sort order
     */
    public function getOrderedBySort(): Collection
    {
        return $this->model->orderBy('sort_order')->get();
    }

    /**
     * Get active stakeholder categories ordered by sort order
     */
    public function getActiveOrderedBySort(): Collection
    {
        return $this->model->active()->orderBy('sort_order')->get();
    }

    /**
     * Search stakeholder categories by name
     */
    public function searchByName(string $name): Collection
    {
        return $this->model->where('name', 'like', "%{$name}%")->get();
    }

    /**
     * Get stakeholder categories with their active subtypes
     */
    public function getWithActiveSubtypes(): Collection
    {
        return $this->model->with(['activeSubtypes' => function ($query) {
            $query->orderBy('sort_order');
        }])->orderBy('sort_order')->get();
    }

    /**
     * Get stakeholder categories with subtype counts
     */
    public function getWithSubtypeCounts(): Collection
    {
        return $this->model->withCount('subtypes')->get();
    }

    /**
     * Check if a category name exists
     */
    public function nameExists(string $name, ?int $excludeId = null): bool
    {
        $query = $this->model->where('name', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if a category slug exists
     */
    public function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get the next sort order value
     */
    public function getNextSortOrder(): int
    {
        return $this->model->max('sort_order') + 1;
    }

    /**
     * Reorder categories
     */
    public function reorder(array $categoryIds): bool
    {
        foreach ($categoryIds as $index => $categoryId) {
            $this->model->where('id', $categoryId)->update(['sort_order' => $index + 1]);
        }

        return true;
    }

    /**
     * Find category by slug with relations
     */
    public function findBySlugWithRelations(string $slug, array $relations = []): ?StakeholderCategory
    {
        $query = $this->model->where('slug', $slug);

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->first();
    }
}
