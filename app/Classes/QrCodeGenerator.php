<?php

declare(strict_types=1);

namespace App\Classes;

use SimpleSoftwareIO\QrCode\Facades\QrCode;

class QrCodeGenerator
{
    public static function generate(string $data, int $size = 300): string
    {
        $svg = QrCode::format('svg')
            ->size($size)
            ->errorCorrection('H')
            ->color(13, 100, 73)
            ->backgroundColor(255, 255, 255)
            ->margin(10)
            ->generate($data);

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    public static function generateForCertificate(string $certificateNumber, string $verificationUrl): string
    {

        return self::generate($verificationUrl, 200);
    }
}
