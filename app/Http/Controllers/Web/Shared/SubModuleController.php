<?php

namespace App\Http\Controllers\Web\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Web\Shared\SubModuleStoreRequest;
use App\Http\Requests\Web\Shared\SubModuleUpdateRequest;
use App\Models\Web\Shared\SubModule;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SubModuleController extends Controller
{
    public function index(Request $request): View
    {
        $subModules = SubModule::all();

        return view('shared.sub-module.index', [
            'subModules' => $subModules,
        ]);
    }

    public function create(Request $request): View
    {
        return view('shared.sub-module.create');
    }

    public function store(SubModuleStoreRequest $request): RedirectResponse
    {
        $subModule = SubModule::create($request->validated());

        $request->session()->flash('subModule.id', $subModule->id);

        return redirect()->route('subModules.index');
    }

    public function show(Request $request, SubModule $subModule): View
    {
        return view('shared.sub-module.show', [
            'subModule' => $subModule,
        ]);
    }

    public function edit(Request $request, SubModule $subModule): View
    {
        return view('shared.sub-module.edit', [
            'subModule' => $subModule,
        ]);
    }

    public function update(SubModuleUpdateRequest $request, SubModule $subModule): RedirectResponse
    {
        $subModule->update($request->validated());

        $request->session()->flash('subModule.id', $subModule->id);

        return redirect()->route('subModules.index');
    }

    public function destroy(Request $request, SubModule $subModule): RedirectResponse
    {
        $subModule->delete();

        return redirect()->route('subModules.index');
    }
}
