<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\ChangePasswordRequest;
use App\Http\Requests\Shared\ProfileUpdateRequest;
use App\Http\Resources\Enacp\BusinessProfileResource;
use App\Http\Resources\Shared\UserResource;
use App\Models\User;
use App\Services\Shared\UserService;
use Carbon\Carbon;
use Dedoc\Scramble\Attributes\Group;
use Dedoc\Scramble\Attributes\Response;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use InvalidArgumentException;

#[Group('Auth API')]
class AuthController extends Controller
{
    public function __construct(
        protected UserService $userService,
    ) {}

    /**
     * AUTH - Login
     *
     * Authenticate user with email and password.
     *
     * @unauthenticated
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string|min:8',
        ]);

        $user = User::where('email', $request->email)->first();

        // Check if user exists
        if (! $user) {
            // Email not found response
            return response()->json([
                'success' => false,
                'message' => 'Email address not found. Please check your email or register a new account.',
            ], 404);
        }

        // Check if password is correct bcrypt()
        if (Hash::check($request->password, $user->password) === false) {
            // Invalid password response
            return response()->json([
                'success' => false,
                'message' => 'Incorrect password. Please check your password and try again.',
                'data' => [
                    'password' => 'Incorrect password. Please check your password and try again.',
                ],
            ], 422);
        }

        // Check if email is verified
        if ($user->isVerified() === false) {
            // Unverified email response
            return response()->json([
                'success' => false,
                'message' => 'Please verify your email address before logging in.',
            ], 500);
        }

        if ($user->isActive() === false) {
            // Inactive account response
            return response()->json([
                'success' => false,
                'message' => 'Your account is NOT active. Please contact support.',
            ], 500);
        }

        if (auth()->attempt($request->only('email', 'password'))) {
            $user = User::where('id', auth()->user()->id)->first();

            // Update last login
            $user->updateLastLogin($request->ip());

            $accessToken = $user->createToken('access_token')->plainTextToken;
            $tokenParts = explode('|', $accessToken);
            $plainToken = $tokenParts[1] ?? null;
            DB::table('personal_access_tokens')
                ->where('token', hash('sha256', $plainToken))
                ->update([
                    'expires_at' => Carbon::now()->addHours(config('settings.security.token_expiration_hours')),
                ]);

            // Successful login response
            return response()->json([
                'success' => true,
                'data' => [
                    'access_token' => $accessToken,
                    'token_type' => 'Bearer',
                    'user' => new UserResource($user),
                    'pm' => 'None',
                ],
            ]);
        }

        // Invalid credentials response
        return response()->json([
            'success' => false,
            'message' => 'The provided credentials are incorrect.',
            'data' => [
                'email' => ['The provided credentials are incorrect.'],
            ],
        ], 422);
    }

    /**
     * AUTH - Logout
     *
     * @response 200 array{success: true, message: string}
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully',
        ]);
    }

    /**
     * AUTH - Get user profile
     *
     * @response 200 array{
     *     success: true,
     *     data: array{
     *         user: UserResource,
     *         business_profile: BusinessProfileResource
     *     }
     * }
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => new UserResource($user),
                'business_profile' => $user->businessProfile
                    ? new BusinessProfileResource($user->businessProfile) : null,
            ],
        ]);
    }

    /**
     * AUTH - Update user profile
     *
     * @response 200 array{success: true, message: string, data: array{user: UserResource}}
     * @response 500 array{success: false, message: string}
     */
    public function updateProfile(ProfileUpdateRequest $request): JsonResponse
    {
        $user = $request->user();

        try {
            $updatedUser = $this->userService->updateProfile($user, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => new UserResource($updatedUser),
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Profile update failed',
            ], 500);
        }
    }

    /**
     * AUTH - Change user password
     *
     * @response 200 array{success: true, message: string}
     * @response 400 array{success: false, message: string}
     * @response 500 array{success: false, message: string}
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $user = $request->user();

        try {
            $this->userService->changePassword(
                $user,
                $request->input('current_password'),
                $request->input('new_password')
            );

            return response()->json([
                'success' => true,
                'message' => 'Password changed successfully',
            ]);
        } catch (InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to change password',
            ], 500);
        }
    }
}
