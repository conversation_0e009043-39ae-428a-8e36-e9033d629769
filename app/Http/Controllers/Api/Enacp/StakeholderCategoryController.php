<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\DTOs\Enacp\CategorySubtypeDTO;
use App\DTOs\Enacp\CategorySubtypeUpdateDTO;
use App\DTOs\Enacp\StakeholderCategoryDTO;
use App\DTOs\Enacp\StakeholderCategoryUpdateDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\CategorySubtypeStoreRequest;
use App\Http\Requests\Enacp\CategorySubtypeUpdateRequest;
use App\Http\Requests\Enacp\StakeholderCategoryStoreRequest;
use App\Http\Requests\Enacp\StakeholderCategoryUpdateRequest;
use App\Http\Resources\Enacp\CategorySubtypeResource;
use App\Http\Resources\Enacp\StakeholderCategoryResource;
use App\Services\Enacp\CategorySubtypeService;
use App\Services\Enacp\StakeholderCategoryService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;

#[Group('ENACP API')]
class StakeholderCategoryController extends Controller
{
    public function __construct(
        protected StakeholderCategoryService $stakeholderCategoryService,
        protected CategorySubtypeService $categorySubtypeService
    ) {
    }

    /**
     * CATEGORY - Get all active stakeholder categories with their subtypes and fees
     * @unauthenticated
     */
    public function index(): JsonResponse
    {
        $categories = $this->stakeholderCategoryService->getActiveWithSubtypes();

        return response()->json([
            'success' => true,
            'data' => StakeholderCategoryResource::collection($categories),
            'message' => 'Stakeholder categories retrieved successfully',
        ]);
    }

    /**
     * CATEGORY - Get a specific stakeholder category with its subtypes
     * @unauthenticated
     */
    public function show(string $slug): JsonResponse
    {
        $category = $this->stakeholderCategoryService->findBySlugWithSubtypes($slug);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new StakeholderCategoryResource($category),
            'message' => 'Category retrieved successfully',
        ]);
    }

    /**
     * CATEGORY - Get categories for admin management (including inactive ones)
     */
    public function adminIndex(): JsonResponse
    {
        $categories = $this->stakeholderCategoryService->getAllWithSubtypes();

        return response()->json([
            'success' => true,
            'data' => StakeholderCategoryResource::collection($categories),
            'message' => 'All categories retrieved successfully',
        ]);
    }

    /**
     * CATEGORY - Create a new stakeholder category
     */
    public function store(StakeholderCategoryStoreRequest $request): JsonResponse
    {
        $dto = StakeholderCategoryDTO::fromRequest($request);
        $category = $this->stakeholderCategoryService->create($dto);

        return response()->json([
            'success' => true,
            'data' => new StakeholderCategoryResource($category),
            'message' => 'Category created successfully',
        ], 201);
    }

    /**
     * CATEGORY - Update a stakeholder category
     */
    public function update(StakeholderCategoryUpdateRequest $request, string $slug): JsonResponse
    {
        $category = $this->stakeholderCategoryService->findBySlug($slug);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
            ], 404);
        }

        $dto = StakeholderCategoryUpdateDTO::fromRequest($request);
        $updatedCategory = $this->stakeholderCategoryService->update($category, $dto);

        return response()->json([
            'success' => true,
            'data' => new StakeholderCategoryResource($updatedCategory),
            'message' => 'Category updated successfully',
        ]);
    }

    /**
     * CATEGORY - Add a new subtype to a category
     */
    public function storeSubtype(CategorySubtypeStoreRequest $request, string $slug): JsonResponse
    {
        $category = $this->stakeholderCategoryService->findBySlug($slug);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
            ], 404);
        }

        $dto = CategorySubtypeDTO::fromRequest($request);
        $subtype = $this->categorySubtypeService->createForCategory($category, $dto);

        return response()->json([
            'success' => true,
            'data' => new CategorySubtypeResource($subtype),
            'message' => 'Subtype added successfully',
        ], 201);
    }

    /**
     * CATEGORY - Update a subtype
     */
    public function updateSubtype(CategorySubtypeUpdateRequest $request, string $slug, int $subtypeId): JsonResponse
    {
        $category = $this->stakeholderCategoryService->findBySlug($slug);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
            ], 404);
        }

        $subtype = $this->categorySubtypeService->findByIdAndCategory($subtypeId, $category->id);

        if (!$subtype) {
            return response()->json([
                'success' => false,
                'message' => 'Subtype not found',
            ], 404);
        }

        $dto = CategorySubtypeUpdateDTO::fromRequest($request);
        $updatedSubtype = $this->categorySubtypeService->update($subtype, $dto);

        return response()->json([
            'success' => true,
            'data' => new CategorySubtypeResource($updatedSubtype),
            'message' => 'Subtype updated successfully',
        ]);
    }

    /**
     * CATEGORY - Delete a subtype
     */
    public function destroySubtype(string $slug, int $subtypeId): JsonResponse
    {
        $category = $this->stakeholderCategoryService->findBySlug($slug);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
            ], 404);
        }

        $subtype = $this->categorySubtypeService->findByIdAndCategory($subtypeId, $category->id);

        if (!$subtype) {
            return response()->json([
                'success' => false,
                'message' => 'Subtype not found',
            ], 404);
        }

        $deleted = $this->categorySubtypeService->delete($subtype);

        if (!$deleted) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete subtype with existing applications',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Subtype deleted successfully',
        ]);
    }
}
