<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\DTOs\Enacp\AccreditationApplicationDTO;
use App\DTOs\Enacp\AccreditationApplicationUpdateDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\AccreditationApplicationStoreRequest;
use App\Http\Requests\Enacp\AccreditationApplicationUpdateRequest;
use App\Http\Resources\Enacp\AccreditationApplicationResource;
use App\Http\Resources\Enacp\CategorySubtypeResource;
use App\Services\Enacp\AccreditationApplicationService;
use App\Services\Enacp\CategorySubtypeService;
use App\Services\Enacp\DocumentService;
use App\Services\Enacp\NotificationService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

#[Group('ENACP API')]
class AccreditationController extends Controller
{
    public function __construct(
        protected AccreditationApplicationService $applicationService,
        protected CategorySubtypeService $categorySubtypeService,
        protected DocumentService $documentService,
        protected NotificationService $notificationService
    ) {
    }

    /**
     * ACCREDITATION - Get user's applications
     */
    public function index(): JsonResponse
    {
        $applications = $this->applicationService->getByUserId(
            Auth::id(),
            ['category', 'subtype', 'documents', 'certificate']
        );

        return response()->json([
            'success' => true,
            'data' => AccreditationApplicationResource::collection($applications),
            'message' => 'Applications retrieved successfully',
        ]);
    }

    /**
     * ACCREDITATION - Get application details
     */
    public function show(string $id): JsonResponse
    {
        $application = $this->applicationService->findByIdAndUserId(
            (int) $id,
            Auth::id(),
            ['category', 'subtype', 'documents', 'certificate']
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new AccreditationApplicationResource($application),
            'message' => 'Application retrieved successfully',
        ]);
    }

    /**
     * ACCREDITATION - Create new application
     */
    public function store(AccreditationApplicationStoreRequest $request): JsonResponse
    {
        // Check if user already has a pending application for this category
        $hasExisting = $this->applicationService->userHasPendingApplicationForCategory(
            Auth::id(),
            $request->integer('stakeholder_category_id')
        );

        if ($hasExisting) {
            return response()->json([
                'success' => false,
                'message' => 'You already have a pending application for this category',
            ], 400);
        }

        $dto = AccreditationApplicationDTO::fromRequest($request);
        $dto = new AccreditationApplicationDTO(
            user_id: Auth::id(),
            stakeholder_category_id: $dto->stakeholder_category_id,
            category_subtype_id: $dto->category_subtype_id,
            application_data: $dto->application_data,
            status: 'draft',
            payment_status: 'pending'
        );

        $application = $this->applicationService->create($dto);

        return response()->json([
            'success' => true,
            'data' => new AccreditationApplicationResource($application->load(['category', 'subtype'])),
            'message' => 'Application created successfully',
        ], 201);
    }

    /**
     * ACCREDITATION - Update application
     */
    public function update(AccreditationApplicationUpdateRequest $request, string $id): JsonResponse
    {
        $application = $this->applicationService->findDraftByIdAndUserId(
            (int) $id,
            Auth::id()
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found or cannot be updated',
            ], 404);
        }

        $dto = AccreditationApplicationUpdateDTO::fromRequest($request);
        $updatedApplication = $this->applicationService->update($application, $dto);

        return response()->json([
            'success' => true,
            'data' => new AccreditationApplicationResource($updatedApplication->load(['category', 'subtype'])),
            'message' => 'Application updated successfully',
        ]);
    }

    /**
     * ACCREDITATION - Submit application
     */
    public function submit(string $id): JsonResponse
    {
        $application = $this->applicationService->findDraftByIdAndUserId(
            (int) $id,
            Auth::id(),
            ['documents', 'category']
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found or cannot be submitted',
            ], 404);
        }

        try {
            $submittedApplication = $this->applicationService->submit($application);

            return response()->json([
                'success' => true,
                'data' => new AccreditationApplicationResource(
                    $submittedApplication->load(['category', 'subtype', 'documents'])
                ),
                'message' => 'Application submitted successfully',
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }



    /**
     * ACCREDITATION - Get application fee details
     * @unauthenticated
     */
    public function getFeeDetails(string $subtypeId): JsonResponse
    {
        $subtype = $this->categorySubtypeService->findById((int) $subtypeId);

        if (!$subtype) {
            return response()->json([
                'success' => false,
                'message' => 'Category subtype not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new CategorySubtypeResource($subtype),
            'message' => 'Fee details retrieved successfully',
        ]);
    }
}
