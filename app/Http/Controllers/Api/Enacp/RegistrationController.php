<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\EmailCheckRequest;
use App\Http\Requests\Enacp\EmailVerificationRequest;
use App\Http\Requests\Enacp\RegistrationRequest;
use App\Http\Requests\Enacp\ResendVerificationRequest;
use App\Http\Resources\Enacp\AccreditationApplicationResource;
use App\Http\Resources\Shared\UserResource;
use App\Services\Enacp\RegistrationService;
use App\Services\Shared\UserService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

#[Group('ENACP API')]
class RegistrationController extends Controller
{
    public function __construct(
        protected RegistrationService $registrationService,
        protected UserService $userService,
    ) {
    }

    /**
     * REGISTRATION - Check if email exists
     * @unauthenticated
     */
    public function checkEmail(EmailCheckRequest $request): JsonResponse
    {
        $email = $request->input('email');
        $exists = $this->userService->emailExists($email);

        return response()->json([
            'success' => true,
            'exists' => $exists,
            'message' => $exists ? 'Email already registered' : 'Email available',
        ]);
    }

    /**
     * REGISTRATION - Register a new user
     * @unauthenticated
     */
    public function register(RegistrationRequest $request): JsonResponse
    {
        try {
            $registrationData = $this->registrationService->registerUser($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Registration successful! Please check your email to verify your account.',
                'data' => [
                    'user' => new UserResource($registrationData['user']),
                    'application' => new AccreditationApplicationResource($registrationData['application']),
                    'token' => $registrationData['token'],
                ],
            ], 201);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed. Please try again.',
            ], 500);
        }
    }

    /**
     * REGISTRATION - Verify email address
     * @unauthenticated
     */
    public function verifyEmail(EmailVerificationRequest $request): JsonResponse
    {
        $token = $request->input('token');

        try {
            $this->registrationService->verifyEmail($token);

            return response()->json([
                'success' => true,
                'message' => 'Email verified successfully',
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error verifying email. Please try again.',
            ], 500);
        }
    }

    /**
     * REGISTRATION - Resend verification email
     * @unauthenticated
     */
    public function resendVerification(ResendVerificationRequest $request): JsonResponse
    {
        $email = $request->input('email');

        try {
            $this->registrationService->resendVerificationEmail($email);

            return response()->json([
                'success' => true,
                'message' => 'Verification email sent successfully',
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification email',
            ], 500);
        }
    }
}
