<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\CertificateVerificationRequest;
use App\Http\Resources\Enacp\CertificateResource;
use App\Services\Enacp\AccreditationApplicationService;
use App\Services\Enacp\CertificateService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;

#[Group('ENACP API')]
class CertificateController extends Controller
{
    public function __construct(
        protected CertificateService $certificateService,
        protected AccreditationApplicationService $applicationService
    ) {
    }

    /**
     * CERTIFICATE - Generate certificate for an approved application
     */
    public function generateCertificate(string $applicationId): JsonResponse
    {
        $application = $this->applicationService->findApprovedByIdWithRelations(
            (int) $applicationId,
            ['user', 'category', 'subtype', 'certificate']
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found or not approved',
            ], 404);
        }

        // Check if certificate already exists
        if ($application->certificate) {
            return response()->json([
                'success' => true,
                'message' => 'Certificate already exists for this application',
                'data' => new CertificateResource($application->certificate),
            ]);
        }

        // Generate certificate
        $certificate = $this->certificateService->generateForApplication($application);

        return response()->json([
            'success' => true,
            'message' => 'Certificate generated successfully',
            'data' => new CertificateResource($certificate),
        ]);
    }

    /**
     * CERTIFICATE - Get certificate for an application
     */
    public function getCertificate(string $applicationId): JsonResponse
    {
        $application = $this->applicationService->findByIdWithRelations(
            (int) $applicationId,
            ['certificate', 'user', 'category', 'subtype']
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found',
            ], 404);
        }

        if (!$application->certificate) {
            return response()->json([
                'success' => false,
                'message' => 'No certificate found for this application',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new CertificateResource($application->certificate),
        ]);
    }

    /**
     * CERTIFICATE - Download certificate PDF
     */
    public function downloadCertificate(string $applicationId): JsonResponse
    {
        $certificate = $this->certificateService->findByApplicationId((int) $applicationId);

        if (!$certificate) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate not found',
            ], 404);
        }

        $downloadUrl = $this->certificateService->getCertificateDownloadUrl($certificate);

        if (!$downloadUrl) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate PDF not available',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'download_url' => $downloadUrl,
            ],
        ]);
    }

    /**
     * CERTIFICATE - Download certificate as image
     */
    public function downloadCertificateImage(string $applicationId): JsonResponse
    {
        $certificate = $this->certificateService->findByApplicationId((int) $applicationId);

        if (!$certificate) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate not found',
            ], 404);
        }

        $downloadUrl = $this->certificateService->getCertificateImageDownloadUrl($certificate);

        if (!$downloadUrl) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate image not available',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'download_url' => $downloadUrl,
            ],
        ]);
    }

    /**
     * CERTIFICATE - Verify certificate by number
     * @unauthenticated
     */
    public function verifyCertificate(CertificateVerificationRequest $request): JsonResponse
    {
        $certificateNumber = $request->input('certificate_number');

        $certificate = $this->certificateService->findByCertificateNumberWithRelations(
            $certificateNumber,
            ['accreditationApplication.user', 'accreditationApplication.category', 'accreditationApplication.subtype']
        );

        if (!$certificate) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate not found',
            ], 404);
        }

        $verificationData = $this->certificateService->getVerificationData($certificate);

        return response()->json([
            'success' => true,
            'data' => array_merge(
                ['certificate' => new CertificateResource($certificate)],
                $verificationData
            ),
        ]);
    }
}
