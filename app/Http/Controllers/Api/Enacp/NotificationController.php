<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\Http\Controllers\Controller;
use App\Http\Resources\Enacp\NotificationResource;
use App\Services\Enacp\NotificationService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

#[Group('ENACP API')]
class NotificationController extends Controller
{
    public function __construct(
        protected NotificationService $notificationService
    ) {
    }

    /**
     * NOTIFICATION - Get user's notifications
     */
    public function index(): JsonResponse
    {
        $notifications = $this->notificationService->getUserNotificationsPaginated(Auth::id(), 20);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => NotificationResource::collection($notifications->items()),
                'pagination' => [
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                    'per_page' => $notifications->perPage(),
                    'total' => $notifications->total(),
                    'from' => $notifications->firstItem(),
                    'to' => $notifications->lastItem(),
                ],
            ],
            'message' => 'Notifications retrieved successfully',
        ]);
    }

    /**
     * NOTIFICATION - Get user's unread notifications
     */
    public function unread(): JsonResponse
    {
        $notifications = $this->notificationService->getUserUnreadNotifications(Auth::id());

        return response()->json([
            'success' => true,
            'data' => NotificationResource::collection($notifications),
            'message' => 'Unread notifications retrieved successfully',
        ]);
    }

    /**
     * NOTIFICATION - Mark notification as read
     */
    public function markAsRead(string $id): JsonResponse
    {
        $notification = $this->notificationService->findUserNotification(Auth::id(), (int) $id);

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found',
            ], 404);
        }

        $updatedNotification = $this->notificationService->markAsRead($notification);

        return response()->json([
            'success' => true,
            'data' => new NotificationResource($updatedNotification),
            'message' => 'Notification marked as read',
        ]);
    }

    /**
     * NOTIFICATION - Mark all notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        $count = $this->notificationService->markAllAsReadForUser(Auth::id());

        return response()->json([
            'success' => true,
            'data' => [
                'marked_count' => $count,
            ],
            'message' => 'All notifications marked as read',
        ]);
    }

    /**
     * NOTIFICATION - Delete notification
     */
    public function destroy(string $id): JsonResponse
    {
        $notification = $this->notificationService->findUserNotification(Auth::id(), (int) $id);

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found',
            ], 404);
        }

        $this->notificationService->delete($notification);

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully',
        ]);
    }
}
