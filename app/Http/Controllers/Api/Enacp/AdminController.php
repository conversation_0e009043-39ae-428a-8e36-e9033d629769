<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\DTOs\Enacp\AccreditationApplicationUpdateDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\AdminApplicationStatusUpdateRequest;
use App\Http\Requests\Enacp\AdminSettingsUpdateRequest;
use App\Http\Requests\Enacp\AdminUserStatusUpdateRequest;
use App\Http\Resources\Enacp\AccreditationApplicationResource;
use App\Http\Resources\Shared\UserResource;
use App\Services\Enacp\AccreditationApplicationService;
use App\Services\Enacp\AdminService;
use App\Services\Enacp\CertificateService;
use App\Services\Enacp\DocumentService;
use App\Services\Enacp\NotificationService;
use App\Services\Shared\UserService;
use Illuminate\Http\JsonResponse;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Support\Facades\Auth;

#[Group('ENACP API')]
class AdminController extends Controller
{
    public function __construct(
        protected AdminService $adminService,
        protected AccreditationApplicationService $applicationService,
        protected UserService $userService,
        protected DocumentService $documentService,
        protected CertificateService $certificateService,
        protected NotificationService $notificationService
    ) {
    }

    /**
     * ADMIN - Get admin dashboard statistics
     */
    public function getStats(): JsonResponse
    {
        $stats = $this->adminService->getDashboardStatistics();

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Statistics retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Get recent applications for admin dashboard
     */
    public function getRecentApplications(): JsonResponse
    {
        $applications = $this->adminService->getRecentApplications(10);

        return response()->json([
            'success' => true,
            'data' => AccreditationApplicationResource::collection($applications),
            'message' => 'Recent applications retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Get all applications for admin review
     */
    public function getApplications(): JsonResponse
    {
        $applications = $this->applicationService->getAllWithRelations(['user', 'category', 'subtype', 'documents']);

        return response()->json([
            'success' => true,
            'data' => AccreditationApplicationResource::collection($applications),
            'message' => 'Applications retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Get specific application details
     */
    public function getApplication(string $applicationSlug): JsonResponse
    {
        $application = $this->applicationService->findByApplicationNumberWithRelations(
            $applicationSlug,
            ['user', 'category', 'subtype', 'documents']
        );

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new AccreditationApplicationResource($application),
            'message' => 'Application retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Update application status
     */
    public function updateApplicationStatus(AdminApplicationStatusUpdateRequest $request, string $applicationId): JsonResponse
    {
        $application = $this->applicationService->findById((int) $applicationId);

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found',
            ], 404);
        }

        $status = $request->input('status');
        $adminNotes = $request->input('admin_notes');

        try {
            if ($status === 'approved') {
                $updatedApplication = $this->applicationService->approve($application, Auth::id(), $adminNotes);
            } elseif ($status === 'rejected') {
                $updatedApplication = $this->applicationService->reject(
                    $application,
                    Auth::id(),
                    $adminNotes ?? 'Application rejected',
                    $adminNotes
                );
            } else {
                $dto = AccreditationApplicationUpdateDTO::fromArray([
                    'status' => $status,
                    'admin_notes' => $adminNotes,
                    'reviewed_by' => Auth::id(),
                ]);
                $updatedApplication = $this->applicationService->update($application, $dto);
            }

            return response()->json([
                'success' => true,
                'data' => new AccreditationApplicationResource(
                    $updatedApplication->load(['user', 'category', 'subtype', 'documents'])
                ),
                'message' => 'Application status updated successfully',
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * ADMIN - Get all users for admin management
     */
    public function getUsers(): JsonResponse
    {
        $users = $this->userService->getAllWithRelations(['accreditationApplications']);

        return response()->json([
            'success' => true,
            'data' => UserResource::collection($users),
            'message' => 'Users retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Get specific user details
     */
    public function getUser(string $userId): JsonResponse
    {
        $user = $this->userService->findByIdWithRelations(
            (int) $userId,
            [
                'accreditationApplications.category',
                'accreditationApplications.subtype',
                'accreditationApplications.documents'
            ]
        );

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new UserResource($user),
            'message' => 'User retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Update user status
     */
    public function updateUserStatus(AdminUserStatusUpdateRequest $request, string $userId): JsonResponse
    {
        $user = $this->userService->findById((int) $userId);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found',
            ], 404);
        }

        $updatedUser = $this->userService->updateStatus(
            $user,
            $request->input('status'),
            $request->input('admin_notes')
        );

        return response()->json([
            'success' => true,
            'data' => new UserResource($updatedUser),
            'message' => 'User status updated successfully',
        ]);
    }

    /**
     * ADMIN - Get admin reports and analytics
     */
    public function getReports(): JsonResponse
    {
        $reports = $this->adminService->getReportsAndAnalytics();

        return response()->json([
            'success' => true,
            'data' => $reports,
            'message' => 'Reports retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Get admin settings
     */
    public function getSettings(): JsonResponse
    {
        $settings = $this->adminService->getSettings();

        return response()->json([
            'success' => true,
            'data' => $settings,
            'message' => 'Settings retrieved successfully',
        ]);
    }

    /**
     * ADMIN - Update admin settings
     */
    public function updateSettings(AdminSettingsUpdateRequest $request): JsonResponse
    {
        $this->adminService->updateSettings($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully',
        ]);
    }
}
