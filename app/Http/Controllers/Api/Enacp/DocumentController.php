<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Enacp;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enacp\DocumentUploadRequest;
use App\Http\Resources\Enacp\DocumentResource;
use App\Services\Enacp\AccreditationApplicationService;
use App\Services\Enacp\DocumentService;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

#[Group('ENACP API')]
class DocumentController extends Controller
{
    public function __construct(
        protected DocumentService $documentService,
        protected AccreditationApplicationService $applicationService
    ) {
    }

    /**
     * DOCUMENT - Upload document
     */
    public function upload(DocumentUploadRequest $request): JsonResponse
    {
        $applicationId = (int) $request->input('application_id');
        $documentType = $request->input('document_type');
        $file = $request->file('file');

        // Check if application belongs to authenticated user and is in draft status
        $application = $this->applicationService->findUserDraftApplication(Auth::id(), $applicationId);

        if (!$application) {
            return response()->json([
                'success' => false,
                'message' => 'Application not found, access denied, or not in draft status',
            ], 404);
        }

        try {
            $document = $this->documentService->uploadForApplication(
                $application,
                $file,
                $documentType
            );

            return response()->json([
                'success' => true,
                'data' => new DocumentResource($document),
                'message' => 'Document uploaded successfully',
            ], 201);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * DOCUMENT - Delete document
     */
    public function delete(string $id): JsonResponse
    {
        $document = $this->documentService->findUserDocument(Auth::id(), (int) $id);

        if (!$document) {
            return response()->json([
                'success' => false,
                'message' => 'Document not found or access denied',
            ], 404);
        }

        try {
            $this->documentService->deleteDocument($document);

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully',
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * DOCUMENT - Download document
     */
    public function download(string $id): JsonResponse
    {
        $document = $this->documentService->findUserDocument(Auth::id(), (int) $id);

        if (!$document) {
            return response()->json([
                'success' => false,
                'message' => 'Document not found or access denied',
            ], 404);
        }

        $downloadUrl = $this->documentService->getDownloadUrl($document);

        if (!$downloadUrl) {
            return response()->json([
                'success' => false,
                'message' => 'File not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'download_url' => $downloadUrl,
                'file_name' => $document->file_name,
            ],
            'message' => 'Download URL generated successfully',
        ]);
    }
}
