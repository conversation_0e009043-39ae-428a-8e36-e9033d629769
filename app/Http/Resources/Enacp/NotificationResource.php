<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use App\Http\Resources\Shared\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'title' => $this->title,
            'message' => $this->message,
            'type' => $this->type,
            'data' => $this->data,
            'is_read' => $this->is_read,
            'read_at' => $this->read_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Computed attributes
            'time_ago' => $this->created_at?->diffForHumans(),
            'is_recent' => $this->when(
                $this->created_at,
                fn() => $this->created_at->isAfter(now()->subHours(24))
            ),

            // Relationships
            'user' => $this->whenLoaded('user', fn() => new UserResource($this->user)),

            // Type-specific data formatting
            'formatted_data' => $this->getFormattedData(),
        ];
    }

    /**
     * Get formatted data based on notification type
     */
    protected function getFormattedData(): array
    {
        $data = $this->data ?? [];

        return match ($this->type) {
            'application_status' => [
                'application_number' => $data['application_number'] ?? null,
                'status' => $data['status'] ?? null,
                'status_label' => $this->getStatusLabel($data['status'] ?? ''),
            ],
            'document_verification' => [
                'document_type' => $data['document_type'] ?? null,
                'document_type_label' => $this->getDocumentTypeLabel($data['document_type'] ?? ''),
                'status' => $data['status'] ?? null,
                'status_label' => $this->getVerificationStatusLabel($data['status'] ?? ''),
                'notes' => $data['notes'] ?? null,
            ],
            'certificate' => [
                'certificate_number' => $data['certificate_number'] ?? null,
                'notification_type' => $data['notification_type'] ?? null,
                'type_label' => $this->getCertificateTypeLabel($data['notification_type'] ?? ''),
            ],
            default => $data,
        };
    }

    /**
     * Get status label
     */
    protected function getStatusLabel(string $status): string
    {
        return match ($status) {
            'draft' => 'Draft',
            'submitted' => 'Submitted',
            'under_review' => 'Under Review',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'expired' => 'Expired',
            default => ucwords(str_replace('_', ' ', $status)),
        };
    }

    /**
     * Get document type label
     */
    protected function getDocumentTypeLabel(string $type): string
    {
        $labels = [
            'cac_certificate' => 'CAC Certificate',
            'tax_clearance' => 'Tax Clearance Certificate',
            'bank_statement' => 'Bank Statement',
            'business_permit' => 'Business Permit',
            'insurance_certificate' => 'Insurance Certificate',
            'audited_accounts' => 'Audited Accounts',
            'memorandum_of_association' => 'Memorandum of Association',
            'articles_of_association' => 'Articles of Association',
            'board_resolution' => 'Board Resolution',
            'other' => 'Other Document',
        ];

        return $labels[$type] ?? ucwords(str_replace('_', ' ', $type));
    }

    /**
     * Get verification status label
     */
    protected function getVerificationStatusLabel(string $status): string
    {
        return match ($status) {
            'pending' => 'Pending Verification',
            'verified' => 'Verified',
            'rejected' => 'Rejected',
            default => ucwords(str_replace('_', ' ', $status)),
        };
    }

    /**
     * Get certificate type label
     */
    protected function getCertificateTypeLabel(string $type): string
    {
        return match ($type) {
            'issued' => 'Certificate Issued',
            'expiring' => 'Certificate Expiring',
            'expired' => 'Certificate Expired',
            'suspended' => 'Certificate Suspended',
            'revoked' => 'Certificate Revoked',
            default => ucwords(str_replace('_', ' ', $type)),
        };
    }
}
