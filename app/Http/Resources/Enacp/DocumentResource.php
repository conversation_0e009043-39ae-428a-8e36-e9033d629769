<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DocumentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'accreditation_application_id' => $this->accreditation_application_id,
            'document_type' => $this->document_type,
            'document_type_label' => $this->getDocumentTypeLabel(),
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'file_size_formatted' => $this->getFormattedFileSize(),
            'mime_type' => $this->mime_type,
            'verification_status' => $this->verification_status,
            'verification_notes' => $this->verification_notes,
            'is_required' => $this->is_required,
            'expiry_date' => $this->expiry_date?->toDateString(),
            'is_expired' => $this->when(
                $this->expiry_date,
                fn() => $this->expiry_date->isPast()
            ),
            'days_until_expiry' => $this->when(
                $this->expiry_date?->isFuture(),
                fn() => now()->diffInDays($this->expiry_date)
            ),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'application' => $this->whenLoaded(
                'accreditationApplication',
                fn() => new AccreditationApplicationResource($this->accreditationApplication)
            ),

            // File URLs (if needed)
            'file_url' => $this->when(
                $this->file_path,
                fn() => \Storage::url($this->file_path)
            ),
            'download_url' => $this->when(
                $this->file_path,
                fn() => route('documents.download', $this->id)
            ),
            'preview_url' => $this->when(
                $this->isPreviewable(),
                fn() => route('documents.preview', $this->id)
            ),
        ];
    }

    /**
     * Get document type label
     */
    protected function getDocumentTypeLabel(): string
    {
        $labels = [
            'cac_certificate' => 'CAC Certificate',
            'tax_clearance' => 'Tax Clearance Certificate',
            'bank_statement' => 'Bank Statement',
            'business_permit' => 'Business Permit',
            'insurance_certificate' => 'Insurance Certificate',
            'audited_accounts' => 'Audited Accounts',
            'memorandum_of_association' => 'Memorandum of Association',
            'articles_of_association' => 'Articles of Association',
            'board_resolution' => 'Board Resolution',
            'other' => 'Other Document',
        ];

        return $labels[$this->document_type] ?? ucwords(str_replace('_', ' ', $this->document_type));
    }

    /**
     * Get formatted file size
     */
    protected function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if document is previewable
     */
    protected function isPreviewable(): bool
    {
        $previewableMimes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
        ];

        return in_array($this->mime_type, $previewableMimes);
    }
}
