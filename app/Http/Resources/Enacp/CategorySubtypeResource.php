<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategorySubtypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'stakeholder_category_id' => $this->stakeholder_category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'accreditation_fee' => $this->accreditation_fee,
            'renewal_fee' => $this->renewal_fee,
            'currency' => $this->currency,
            'formatted_accreditation_fee' => $this->currency . ' ' . number_format($this->accreditation_fee, 2),
            'formatted_renewal_fee' => $this->currency . ' ' . number_format($this->renewal_fee, 2),
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'category' => $this->whenLoaded(
                'category',
                fn() => new StakeholderCategoryResource($this->category)
            ),

            // Counts
            'applications_count' => $this->when(
                isset($this->applications_count),
                $this->applications_count
            ),
        ];
    }
}
