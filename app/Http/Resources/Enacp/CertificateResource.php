<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CertificateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'accreditation_application_id' => $this->accreditation_application_id,
            'certificate_number' => $this->certificate_number,
            'qr_code_path' => $this->qr_code_path,
            'certificate_path' => $this->certificate_path,
            'certificate_image_path' => $this->certificate_image_path,
            'issued_date' => $this->issued_date?->toDateString(),
            'expiry_date' => $this->expiry_date?->toDateString(),
            'status' => $this->status,
            'certificate_data' => $this->certificate_data,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Computed attributes
            'is_expired' => $this->when(
                $this->expiry_date,
                fn() => $this->expiry_date->isPast()
            ),
            'is_expiring_soon' => $this->when(
                $this->expiry_date,
                fn() => $this->expiry_date->isFuture() &&
                       now()->diffInDays($this->expiry_date) <= 30
            ),
            'days_until_expiry' => $this->when(
                $this->expiry_date?->isFuture(),
                fn() => now()->diffInDays($this->expiry_date)
            ),
            'validity_period_days' => $this->when(
                $this->issued_date && $this->expiry_date,
                fn() => $this->issued_date?->diffInDays($this->expiry_date)
            ),

            // URLs
            'qr_code_url' => $this->when(
                $this->qr_code_path,
                fn() => \Storage::url($this->qr_code_path)
            ),
            'certificate_url' => $this->when(
                $this->certificate_path,
                fn() => \Storage::url($this->certificate_path)
            ),
            'certificate_image_url' => $this->when(
                $this->certificate_image_path,
                fn() => \Storage::url($this->certificate_image_path)
            ),
            'verification_url' => $this->when(
                $this->certificate_number,
                fn() => route('certificates.verify', $this->certificate_number)
            ),

            // Relationships
            'application' => $this->whenLoaded(
                'accreditationApplication',
                fn() => new AccreditationApplicationResource($this->accreditationApplication)
            ),

            // Certificate holder information (from certificate_data)
            'holder_name' => $this->when(
                isset($this->certificate_data['holder_name']),
                $this->certificate_data['holder_name'] ?? null
            ),
            'company_name' => $this->when(
                isset($this->certificate_data['company_name']),
                $this->certificate_data['company_name'] ?? null
            ),
            'category' => $this->when(
                isset($this->certificate_data['category']),
                $this->certificate_data['category'] ?? null
            ),
            'subtype' => $this->when(
                isset($this->certificate_data['subtype']),
                $this->certificate_data['subtype'] ?? null
            ),
        ];
    }
}
