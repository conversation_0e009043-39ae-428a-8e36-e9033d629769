<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StakeholderCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'icon' => $this->icon,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'subtypes' => $this->whenLoaded(
                'subtypes',
                fn() => CategorySubtypeResource::collection($this->subtypes)
            ),
            'active_subtypes' => $this->whenLoaded(
                'activeSubtypes',
                fn() => CategorySubtypeResource::collection($this->activeSubtypes)
            ),

            // Counts
            'subtypes_count' => $this->when(
                isset($this->subtypes_count),
                $this->subtypes_count
            ),
            'applications_count' => $this->when(
                isset($this->applications_count),
                $this->applications_count
            ),
        ];
    }
}
