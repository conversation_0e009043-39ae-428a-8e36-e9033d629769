<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use App\Http\Resources\Shared\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccreditationApplicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'stakeholder_category_id' => $this->stakeholder_category_id,
            'category_subtype_id' => $this->category_subtype_id,
            'application_number' => $this->application_number,
            'status' => $this->status,
            'status_updated_at' => $this->status_updated_at?->toISOString(),
            'fee_paid' => $this->fee_paid,
            'payment_reference' => $this->payment_reference,
            'payment_status' => $this->payment_status,
            'fee_amount' => $this->fee_amount,
            'application_data' => $this->application_data,
            'submitted_at' => $this->submitted_at?->toDateString(),
            'admin_notes' => $this->admin_notes,
            'rejection_reason' => $this->rejection_reason,
            'reviewed_at' => $this->reviewed_at?->toISOString(),
            'reviewed_by' => $this->reviewed_by,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'user' => $this->whenLoaded('user', fn() => new UserResource($this->user)),
            'category' => $this->whenLoaded('category', fn() => new StakeholderCategoryResource($this->category)),
            'subtype' => $this->whenLoaded('subtype', fn() => new CategorySubtypeResource($this->subtype)),
            'documents' => $this->whenLoaded('documents', fn() => DocumentResource::collection($this->documents)),
            'certificate' => $this->whenLoaded('certificate', fn() => new CertificateResource($this->certificate)),
            'reviewer' => $this->whenLoaded('reviewer', fn() => new UserResource($this->reviewer)),

            // Counts
            'documents_count' => $this->when(isset($this->documents_count), $this->documents_count),
            'required_documents_count' => $this->when(
                isset($this->required_documents_count),
                $this->required_documents_count
            ),
            'verified_documents_count' => $this->when(
                isset($this->verified_documents_count),
                $this->verified_documents_count
            ),

            // Computed attributes
            'is_complete' => $this->when(
                $this->relationLoaded('documents'),
                fn() => $this->documents->where('is_required', true)->count() > 0
            ),
            'can_be_submitted' => $this->when(
                $this->status === 'draft',
                fn() => !empty($this->application_data) &&
                       ($this->relationLoaded('documents') ?
                        $this->documents->where('is_required', true)->count() > 0 : true)
            ),
        ];
    }
}
