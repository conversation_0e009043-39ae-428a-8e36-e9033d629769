<?php

declare(strict_types=1);

namespace App\Http\Resources\Enacp;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_name' => $this->company_name,
            'rc_number' => $this->rc_number,
            'tax_id' => $this->tax_id,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'website' => $this->website,
            'position' => $this->position,
            'business_type' => $this->business_type,
            'years_in_business' => $this->years_in_business,
            'annual_revenue' => $this->getFormattedAnnualRevenueAttribute(),
            'formatted_annual_revenue' => $this->formatted_annual_revenue,
            'number_of_employees' => $this->number_of_employees,
            'business_description' => $this->business_description,
            'category_subtype_id' => $this->category_subtype_id,
            'payment_reference' => $this->payment_reference,
            'created_by' => $this->created_by,
            'modified_by' => $this->modified_by,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'created_by_user' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->fullname(),
                    'email' => $this->createdBy->email,
                ];
            }),
            'modified_by_user' => $this->whenLoaded('modifiedBy', function () {
                return [
                    'id' => $this->modifiedBy->id,
                    'name' => $this->modifiedBy->fullname(),
                    'email' => $this->modifiedBy->email,
                ];
            }),
            'category_subtype' => $this->whenLoaded('categorySubtype', function () {
                return new CategorySubtypeResource($this->categorySubtype);
            }),
        ];
    }
}
