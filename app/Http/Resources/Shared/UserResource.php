<?php

namespace App\Http\Resources\Shared;

use App\Http\Resources\AccessControl\RoleResource;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'identifier' => $this->identifier,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'profile_picture' => $this->profile_picture,
            'status' => $this->status,
            'is_verified' => $this->isVerified(),

            // Relationships.
            'role' => new RoleResource($this->roles->first()),
        ];
    }
}
