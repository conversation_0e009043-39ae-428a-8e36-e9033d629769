<?php

namespace App\Http\Requests\Web\AccessControl;

use Illuminate\Foundation\Http\FormRequest;

class PermissionUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'sub_module_id' => ['required', 'integer', 'exists:sub_modules,id'],
            'name' => ['required', 'string'],
            'action' => ['required', 'string'],
            'description' => ['nullable', 'string'],
        ];
    }
}
