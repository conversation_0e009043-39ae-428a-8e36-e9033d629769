<?php

namespace App\Http\Requests\Web\Shared;

use Illuminate\Foundation\Http\FormRequest;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'avatar' => ['nullable', 'mimes:png,jpg,jpeg', 'max:2048'],
            'avatar_remove' => ['nullable', 'in:0,1'],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email,' . $this->user->id],
            'phone' => ['nullable', 'string', 'max:255', 'unique:users,phone,' . $this->user->id],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            'role_id' => ['nullable', 'exists:roles,id'],
            'status' => ['nullable', 'in:pending,active,suspended,inactive'],
        ];
    }

    public function messages(): array
    {
        return [
            'avatar.required' => 'A profile picture is required.',
        ];
    }
}
