<?php

namespace App\Http\Requests\Web\Shared\Auth;

use App\Models\User;
use App\Services\Shared\UserService;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class WebLoginRequest extends FormRequest
{
    public function __construct(protected UserService $service)
    {
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'credential' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate()
    {
        $this->ensureIsNotRateLimited();

        $credential = $this->input('credential');
        $password = $this->input('password');
        $remember = $this->boolean('remember');

        // Determine if credential is an email
        $field = filter_var($credential, FILTER_VALIDATE_EMAIL) ? 'email' : 'identifier';

        // First check if user exists and is active before attempting authentication
        $user = User::where($field, $credential)->first();

        if (!$user) {
            RateLimiter::hit($this->throttleKey());
            throw ValidationException::withMessages([
                'credential' => __('auth.failed'),
            ]);
        }

        if ($user->status !== 'active') {
            RateLimiter::hit($this->throttleKey());
            throw ValidationException::withMessages([
                'credential' => 'Your account has is not active. Please contact support.',
            ]);
        }

        if (!Auth::attempt([$field => $credential, 'password' => $password], $remember)) {
            RateLimiter::hit($this->throttleKey());
            throw ValidationException::withMessages([
                'credential' => __('auth.password'),
            ]);
        }

        $user = auth()->user();
        $accesses = ['first_access' => time(), 'last_login_at' => time()];
        if ($user->first_access) {
            unset($accesses['first_access']);
        }
        $this->service->update($user, $accesses);

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited()
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'credential' => trans('shared.auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     *
     * @return string
     */
    public function throttleKey()
    {
        return Str::lower($this->input('credential')) . '|' . $this->ip();
    }
}
