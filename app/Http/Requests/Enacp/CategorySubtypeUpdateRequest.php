<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CategorySubtypeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $subtypeId = $this->route('category_subtype')?->id ?? $this->route('id');

        return [
            'stakeholder_category_id' => 'sometimes|required|exists:stakeholder_categories,id',
            'name' => 'sometimes|required|string|max:255',
            'slug' => [
                'sometimes',
                'nullable',
                'string',
                'max:255',
                Rule::unique('category_subtypes', 'slug')->ignore($subtypeId),
            ],
            'accreditation_fee' => 'sometimes|required|numeric|min:0|max:999999999999.99',
            'renewal_fee' => 'sometimes|required|numeric|min:0|max:999999999999.99',
            'currency' => 'sometimes|required|string|size:3|in:NGN,USD,EUR,GBP',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'stakeholder_category_id.required' => 'Stakeholder category is required.',
            'stakeholder_category_id.exists' => 'Selected stakeholder category is invalid.',
            'name.required' => 'Subtype name is required.',
            'name.max' => 'Subtype name cannot exceed 255 characters.',
            'slug.unique' => 'A subtype with this slug already exists.',
            'slug.max' => 'Slug cannot exceed 255 characters.',
            'accreditation_fee.required' => 'Accreditation fee is required.',
            'accreditation_fee.numeric' => 'Accreditation fee must be a number.',
            'accreditation_fee.min' => 'Accreditation fee cannot be negative.',
            'accreditation_fee.max' => 'Accreditation fee is too large.',
            'renewal_fee.required' => 'Renewal fee is required.',
            'renewal_fee.numeric' => 'Renewal fee must be a number.',
            'renewal_fee.min' => 'Renewal fee cannot be negative.',
            'renewal_fee.max' => 'Renewal fee is too large.',
            'currency.required' => 'Currency is required.',
            'currency.size' => 'Currency must be exactly 3 characters.',
            'currency.in' => 'Currency must be one of: NGN, USD, EUR, GBP.',
            'is_active.boolean' => 'Active status must be true or false.',
            'sort_order.integer' => 'Sort order must be a number.',
            'sort_order.min' => 'Sort order cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'stakeholder_category_id' => 'stakeholder category',
            'name' => 'subtype name',
            'accreditation_fee' => 'accreditation fee',
            'renewal_fee' => 'renewal fee',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('name') && !$this->has('slug')) {
            $this->merge([
                'slug' => \Str::slug($this->input('name')),
            ]);
        }
    }
}
