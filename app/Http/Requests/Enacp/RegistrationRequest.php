<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20',
            'password' => ['required', 'confirmed', Password::defaults()],
            'company_name' => 'required|string|max:255',
            'rc_number' => 'nullable|string|max:50',
            'tax_id' => 'nullable|string|max:50',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'position' => 'required|string|max:100',
            'business_type' => 'required|string|max:100',
            'years_in_business' => 'required|integer|min:0|max:100',
            'annual_revenue' => 'required|numeric|min:0',
            'number_of_employees' => 'required|integer|min:1',
            'business_description' => 'required|string|max:1000',
            'category_subtype_id' => 'required|exists:category_subtypes,id',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'payment_reference' => 'required|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'first_name.string' => 'First name must be a valid string.',
            'first_name.max' => 'First name cannot exceed 255 characters.',
            'last_name.required' => 'Last name is required.',
            'last_name.string' => 'Last name must be a valid string.',
            'last_name.max' => 'Last name cannot exceed 255 characters.',
            'email.required' => 'Email address is required.',
            'email.string' => 'Email must be a valid string.',
            'email.email' => 'Please provide a valid email address.',
            'email.max' => 'Email address cannot exceed 255 characters.',
            'email.unique' => 'This email address is already registered.',
            'phone.required' => 'Phone number is required.',
            'phone.string' => 'Phone number must be a valid string.',
            'phone.max' => 'Phone number cannot exceed 20 characters.',
            'password.required' => 'Password is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'company_name.required' => 'Company name is required.',
            'company_name.max' => 'Company name cannot exceed 255 characters.',
            'rc_number.max' => 'RC number cannot exceed 50 characters.',
            'tax_id.max' => 'Tax ID cannot exceed 50 characters.',
            'address.required' => 'Address is required.',
            'address.max' => 'Address cannot exceed 500 characters.',
            'city.required' => 'City is required.',
            'city.max' => 'City cannot exceed 100 characters.',
            'state.required' => 'State is required.',
            'state.max' => 'State cannot exceed 100 characters.',
            'country.required' => 'Country is required.',
            'country.max' => 'Country cannot exceed 100 characters.',
            'postal_code.max' => 'Postal code cannot exceed 20 characters.',
            'website.url' => 'Website must be a valid URL.',
            'website.max' => 'Website cannot exceed 255 characters.',
            'position.required' => 'Position is required.',
            'position.max' => 'Position cannot exceed 100 characters.',
            'business_type.required' => 'Business type is required.',
            'business_type.max' => 'Business type cannot exceed 100 characters.',
            'years_in_business.required' => 'Years in business is required.',
            'years_in_business.integer' => 'Years in business must be a number.',
            'years_in_business.min' => 'Years in business cannot be negative.',
            'years_in_business.max' => 'Years in business cannot exceed 100.',
            'annual_revenue.required' => 'Annual revenue is required.',
            'annual_revenue.numeric' => 'Annual revenue must be a number.',
            'annual_revenue.min' => 'Annual revenue cannot be negative.',
            'number_of_employees.required' => 'Number of employees is required.',
            'number_of_employees.integer' => 'Number of employees must be a number.',
            'number_of_employees.min' => 'Number of employees must be at least 1.',
            'business_description.required' => 'Business description is required.',
            'business_description.max' => 'Business description cannot exceed 1000 characters.',
            'category_subtype_id.required' => 'Category subtype is required.',
            'category_subtype_id.exists' => 'Selected category subtype is invalid.',
            'profile_picture.image' => 'Profile picture must be an image.',
            'profile_picture.mimes' => 'Profile picture must be a JPEG, PNG, or JPG file.',
            'profile_picture.max' => 'Profile picture cannot exceed 2MB.',
            'payment_reference.required' => 'Payment reference is required.',
            'payment_reference.max' => 'Payment reference cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => 'email address',
            'phone' => 'phone number',
            'password' => 'password',
            'company_name' => 'company name',
            'rc_number' => 'RC number',
            'tax_id' => 'tax ID',
            'postal_code' => 'postal code',
            'years_in_business' => 'years in business',
            'annual_revenue' => 'annual revenue',
            'number_of_employees' => 'number of employees',
            'business_description' => 'business description',
            'category_subtype_id' => 'category subtype',
            'profile_picture' => 'profile picture',
            'payment_reference' => 'payment reference',
        ];
    }
}
