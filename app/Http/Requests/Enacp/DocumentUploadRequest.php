<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class DocumentUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:2048', // 2MB max
            'document_type' => 'required|string|in:cac_certificate,tax_clearance,bank_statement,financial_statement,professional_certificate,cv_resume,reference_letter,business_plan,insurance_certificate,other',
            'application_id' => 'required|integer|exists:accreditation_applications,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Please select a file to upload.',
            'file.file' => 'The uploaded item must be a valid file.',
            'file.mimes' => 'Only PDF, DOC, DOCX, JPG, JPEG, and PNG files are allowed.',
            'file.max' => 'File size cannot exceed 2MB.',
            'document_type.required' => 'Document type is required.',
            'document_type.in' => 'Invalid document type selected.',
            'application_id.required' => 'Application ID is required.',
            'application_id.integer' => 'Application ID must be a valid number.',
            'application_id.exists' => 'The selected application does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'document file',
            'document_type' => 'document type',
            'application_id' => 'application',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation can be added here if needed
            $file = $this->file('file');

            if ($file && $file->isValid()) {
                // Check file extension matches MIME type for security
                $allowedMimes = [
                    'pdf' => 'application/pdf',
                    'doc' => 'application/msword',
                    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'jpg' => 'image/jpeg',
                    'jpeg' => 'image/jpeg',
                    'png' => 'image/png',
                ];

                $extension = strtolower($file->getClientOriginalExtension());
                $mimeType = $file->getMimeType();

                if (isset($allowedMimes[$extension]) && $allowedMimes[$extension] !== $mimeType) {
                    $validator->errors()->add('file', 'File type does not match its extension.');
                }
            }
        });
    }
}
