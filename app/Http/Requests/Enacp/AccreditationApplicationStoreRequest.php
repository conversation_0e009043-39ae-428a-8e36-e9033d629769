<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;

use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class AccreditationApplicationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'stakeholder_category_id' => 'required|exists:stakeholder_categories,id',
            'category_subtype_id' => 'required|exists:category_subtypes,id',
            'application_number' => 'nullable|string|max:50|unique:accreditation_applications,application_number',
            'status' => 'required|in:draft,submitted,under_review,approved,rejected,expired',
            'fee_paid' => 'numeric|min:0|max:999999999999.99',
            'payment_status' => 'required|in:pending,paid,failed',
            'fee_amount' => 'nullable|numeric|min:0|max:999999999999.99',
            'application_data' => 'required|array',
            'application_data.business_profile' => 'required|array',
            'application_data.documents' => 'nullable|array',
            'submitted_at' => 'nullable|date',
            'admin_notes' => 'nullable|string|max:2000',
            'rejection_reason' => 'nullable|string|max:1000',
            'payment_reference' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User is required.',
            'user_id.exists' => 'Selected user is invalid.',
            'stakeholder_category_id.required' => 'Stakeholder category is required.',
            'stakeholder_category_id.exists' => 'Selected stakeholder category is invalid.',
            'category_subtype_id.required' => 'Category subtype is required.',
            'category_subtype_id.exists' => 'Selected category subtype is invalid.',
            'application_number.unique' => 'Application number already exists.',
            'application_number.max' => 'Application number cannot exceed 50 characters.',
            'status.required' => 'Application status is required.',
            'status.in' => 'Invalid application status.',
            'fee_paid.numeric' => 'Fee paid must be a number.',
            'fee_paid.min' => 'Fee paid cannot be negative.',
            'fee_paid.max' => 'Fee paid amount is too large.',
            'payment_status.required' => 'Payment status is required.',
            'payment_status.in' => 'Invalid payment status.',
            'fee_amount.numeric' => 'Fee amount must be a number.',
            'fee_amount.min' => 'Fee amount cannot be negative.',
            'fee_amount.max' => 'Fee amount is too large.',
            'application_data.required' => 'Application data is required.',
            'application_data.array' => 'Application data must be an array.',
            'application_data.business_profile.required' => 'Business profile data is required.',
            'application_data.business_profile.array' => 'Business profile data must be an array.',
            'application_data.documents.array' => 'Documents data must be an array.',
            'submitted_at.date' => 'Submitted date must be a valid date.',
            'admin_notes.max' => 'Admin notes cannot exceed 2000 characters.',
            'rejection_reason.max' => 'Rejection reason cannot exceed 1000 characters.',
            'payment_reference.max' => 'Payment reference cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'user',
            'stakeholder_category_id' => 'stakeholder category',
            'category_subtype_id' => 'category subtype',
            'application_number' => 'application number',
            'fee_paid' => 'fee paid',
            'payment_status' => 'payment status',
            'fee_amount' => 'fee amount',
            'application_data' => 'application data',
            'submitted_at' => 'submitted date',
            'admin_notes' => 'admin notes',
            'rejection_reason' => 'rejection reason',
            'payment_reference' => 'payment reference',
        ];
    }
}
