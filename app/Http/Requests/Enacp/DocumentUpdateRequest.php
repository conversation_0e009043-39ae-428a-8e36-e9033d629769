<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class DocumentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accreditation_application_id' => 'sometimes|required|exists:accreditation_applications,id',
            'document_type' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                'in:cac_certificate,tax_clearance,bank_statement,business_permit,insurance_certificate,
                    audited_accounts,memorandum_of_association,articles_of_association,board_resolution,other'
            ],
            'document' => 'sometimes|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB max
            'verification_status' => 'sometimes|in:pending,verified,rejected',
            'is_required' => 'sometimes|boolean',
            'expiry_date' => 'sometimes|nullable|date|after:today',
            'verification_notes' => 'sometimes|nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'accreditation_application_id.required' => 'Accreditation application is required.',
            'accreditation_application_id.exists' => 'Selected accreditation application is invalid.',
            'document_type.required' => 'Document type is required.',
            'document_type.max' => 'Document type cannot exceed 100 characters.',
            'document_type.in' => 'Invalid document type selected.',
            'document.file' => 'Document must be a valid file.',
            'document.mimes' => 'Document must be a PDF, DOC, DOCX, JPG, JPEG, or PNG file.',
            'document.max' => 'Document file cannot exceed 10MB.',
            'verification_status.in' => 'Invalid verification status.',
            'is_required.boolean' => 'Required status must be true or false.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be in the future.',
            'verification_notes.max' => 'Verification notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'accreditation_application_id' => 'accreditation application',
            'document_type' => 'document type',
            'verification_status' => 'verification status',
            'is_required' => 'required status',
            'expiry_date' => 'expiry date',
            'verification_notes' => 'verification notes',
        ];
    }

    /**
     * Get the document types with their display names.
     */
    public static function getDocumentTypes(): array
    {
        return [
            'cac_certificate' => 'CAC Certificate',
            'tax_clearance' => 'Tax Clearance Certificate',
            'bank_statement' => 'Bank Statement',
            'business_permit' => 'Business Permit',
            'insurance_certificate' => 'Insurance Certificate',
            'audited_accounts' => 'Audited Accounts',
            'memorandum_of_association' => 'Memorandum of Association',
            'articles_of_association' => 'Articles of Association',
            'board_resolution' => 'Board Resolution',
            'other' => 'Other Document',
        ];
    }
}
