<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;

use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class AdminSettingsUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->isSuperAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'system' => 'sometimes|array',
            'system.site_name' => 'sometimes|string|max:255',
            'system.site_description' => 'sometimes|string',
            'system.maintenance_mode' => 'sometimes|boolean',
            'system.registration_enabled' => 'sometimes|boolean',
            'system.max_file_size' => 'sometimes|integer|min:1|max:100',
            'system.allowed_file_types' => 'sometimes|string',

            'email' => 'sometimes|array',
            'email.smtp_host' => 'sometimes|string|max:255',
            'email.smtp_port' => 'sometimes|integer|min:1|max:65535',
            'email.smtp_username' => 'sometimes|string|max:255',
            'email.smtp_password' => 'sometimes|string',
            'email.from_email' => 'sometimes|email|max:255',
            'email.from_name' => 'sometimes|string|max:255',

            'notifications' => 'sometimes|array',
            'notifications.email_notifications' => 'sometimes|boolean',
            'notifications.application_submitted' => 'sometimes|boolean',
            'notifications.application_approved' => 'sometimes|boolean',
            'notifications.application_rejected' => 'sometimes|boolean',
            'notifications.certificate_generated' => 'sometimes|boolean',
            'notifications.renewal_reminder' => 'sometimes|boolean',

            'fees' => 'sometimes|array',
            'fees.default_currency' => 'sometimes|string|size:3',
            'fees.payment_gateway' => 'sometimes|string|max:50',
            'fees.auto_approve_payments' => 'sometimes|boolean',
            'fees.refund_enabled' => 'sometimes|boolean',

            'security' => 'sometimes|array',
            'security.session_timeout' => 'sometimes|integer|min:5|max:480',
            'security.max_login_attempts' => 'sometimes|integer|min:3|max:10',
            'security.password_min_length' => 'sometimes|integer|min:6|max:20',
            'security.require_strong_password' => 'sometimes|boolean',
            'security.two_factor_auth' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'system.site_name.max' => 'Site name cannot exceed 255 characters.',
            'system.max_file_size.min' => 'Maximum file size must be at least 1 MB.',
            'system.max_file_size.max' => 'Maximum file size cannot exceed 100 MB.',
            'email.smtp_port.min' => 'SMTP port must be at least 1.',
            'email.smtp_port.max' => 'SMTP port cannot exceed 65535.',
            'email.from_email.email' => 'From email must be a valid email address.',
            'fees.default_currency.size' => 'Currency code must be exactly 3 characters.',
            'security.session_timeout.min' => 'Session timeout must be at least 5 minutes.',
            'security.session_timeout.max' => 'Session timeout cannot exceed 480 minutes (8 hours).',
            'security.max_login_attempts.min' => 'Maximum login attempts must be at least 3.',
            'security.max_login_attempts.max' => 'Maximum login attempts cannot exceed 10.',
            'security.password_min_length.min' => 'Password minimum length must be at least 6 characters.',
            'security.password_min_length.max' => 'Password minimum length cannot exceed 20 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'system.site_name' => 'site name',
            'system.site_description' => 'site description',
            'system.maintenance_mode' => 'maintenance mode',
            'system.registration_enabled' => 'registration enabled',
            'system.max_file_size' => 'maximum file size',
            'system.allowed_file_types' => 'allowed file types',
            'email.smtp_host' => 'SMTP host',
            'email.smtp_port' => 'SMTP port',
            'email.smtp_username' => 'SMTP username',
            'email.smtp_password' => 'SMTP password',
            'email.from_email' => 'from email',
            'email.from_name' => 'from name',
            'notifications.email_notifications' => 'email notifications',
            'fees.default_currency' => 'default currency',
            'fees.payment_gateway' => 'payment gateway',
            'fees.auto_approve_payments' => 'auto approve payments',
            'fees.refund_enabled' => 'refund enabled',
            'security.session_timeout' => 'session timeout',
            'security.max_login_attempts' => 'maximum login attempts',
            'security.password_min_length' => 'password minimum length',
            'security.require_strong_password' => 'require strong password',
            'security.two_factor_auth' => 'two factor authentication',
        ];
    }
}
