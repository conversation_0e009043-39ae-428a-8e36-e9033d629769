<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;

use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class AdminApplicationStatusUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->isSuperAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => 'required|string|in:approved,rejected,submitted,under_review',
            'admin_notes' => 'nullable|string|max:1000',
            'rejection_reason' => 'required_if:status,rejected|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Application status is required.',
            'status.in' => 'Invalid application status selected.',
            'admin_notes.max' => 'Admin notes cannot exceed 1000 characters.',
            'rejection_reason.required_if' => 'Rejection reason is required when rejecting an application.',
            'rejection_reason.max' => 'Rejection reason cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'status' => 'application status',
            'admin_notes' => 'admin notes',
            'rejection_reason' => 'rejection reason',
        ];
    }
}
