<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class CertificateStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accreditation_application_id' => 'required|exists:accreditation_applications,id',
            'certificate_number' => 'required|string|max:100|unique:accreditation_certificates,certificate_number',
            'qr_code_path' => 'nullable|string|max:500',
            'certificate_path' => 'nullable|string|max:500',
            'certificate_image_path' => 'nullable|string|max:500',
            'issued_date' => 'required|date',
            'expiry_date' => 'required|date|after:issued_date',
            'status' => 'required|in:active,suspended,revoked,expired',
            'certificate_data' => 'nullable|array',
            'certificate_data.holder_name' => 'sometimes|required|string|max:255',
            'certificate_data.company_name' => 'sometimes|required|string|max:255',
            'certificate_data.category' => 'sometimes|required|string|max:255',
            'certificate_data.subtype' => 'sometimes|required|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'accreditation_application_id.required' => 'Accreditation application is required.',
            'accreditation_application_id.exists' => 'Selected accreditation application is invalid.',
            'certificate_number.required' => 'Certificate number is required.',
            'certificate_number.unique' => 'Certificate number already exists.',
            'certificate_number.max' => 'Certificate number cannot exceed 100 characters.',
            'qr_code_path.max' => 'QR code path cannot exceed 500 characters.',
            'certificate_path.max' => 'Certificate path cannot exceed 500 characters.',
            'certificate_image_path.max' => 'Certificate image path cannot exceed 500 characters.',
            'issued_date.required' => 'Issued date is required.',
            'issued_date.date' => 'Issued date must be a valid date.',
            'expiry_date.required' => 'Expiry date is required.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be after the issued date.',
            'status.required' => 'Certificate status is required.',
            'status.in' => 'Invalid certificate status.',
            'certificate_data.array' => 'Certificate data must be an array.',
            'certificate_data.holder_name.required' => 'Holder name is required in certificate data.',
            'certificate_data.holder_name.max' => 'Holder name cannot exceed 255 characters.',
            'certificate_data.company_name.required' => 'Company name is required in certificate data.',
            'certificate_data.company_name.max' => 'Company name cannot exceed 255 characters.',
            'certificate_data.category.required' => 'Category is required in certificate data.',
            'certificate_data.category.max' => 'Category cannot exceed 255 characters.',
            'certificate_data.subtype.required' => 'Subtype is required in certificate data.',
            'certificate_data.subtype.max' => 'Subtype cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'accreditation_application_id' => 'accreditation application',
            'certificate_number' => 'certificate number',
            'qr_code_path' => 'QR code path',
            'certificate_path' => 'certificate path',
            'certificate_image_path' => 'certificate image path',
            'issued_date' => 'issued date',
            'expiry_date' => 'expiry date',
            'certificate_data' => 'certificate data',
            'certificate_data.holder_name' => 'holder name',
            'certificate_data.company_name' => 'company name',
            'certificate_data.category' => 'category',
            'certificate_data.subtype' => 'subtype',
        ];
    }
}
