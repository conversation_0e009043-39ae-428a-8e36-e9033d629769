<?php

declare(strict_types=1);

namespace App\Http\Requests\Enacp;


use Dedoc\Scramble\Attributes\SchemaName;
use Illuminate\Foundation\Http\FormRequest;

class CertificateVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Certificate verification is public, no authentication required
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'certificate_number' => 'required|string|min:5|max:50',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'certificate_number.required' => 'Certificate number is required.',
            'certificate_number.string' => 'Certificate number must be a valid string.',
            'certificate_number.min' => 'Certificate number must be at least 5 characters long.',
            'certificate_number.max' => 'Certificate number cannot exceed 50 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'certificate_number' => 'certificate number',
        ];
    }
}
