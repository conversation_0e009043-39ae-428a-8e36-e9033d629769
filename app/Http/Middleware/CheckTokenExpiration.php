<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CheckTokenExpiration
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();
        $tokenParts = explode('|', $token);
        $plainToken = $tokenParts[1] ?? null;
        // Log::info("Authorization Header Token", ['token' => $token]);

        if (!$token) {
            return response()->json(['message' => 'No token provided'], 401);
        }

        $hashedToken = hash('sha256', $plainToken);

        $personalAccessToken = DB::table('personal_access_tokens')
            ->where('token', $hashedToken)
            ->first();

        if (!$personalAccessToken) {
            return response()->json(['message' => 'Token not found'], 401);
        }

        if (Carbon::now()->greaterThan($personalAccessToken->expires_at)) {
            return response()->json(['message' => 'Token expired'], 401);
        }

        return $next($request);
    }
}
