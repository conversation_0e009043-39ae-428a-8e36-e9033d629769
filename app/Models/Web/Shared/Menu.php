<?php

namespace App\Models\Web\Shared;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property string $title
 * @property string|null $icon
 * @property string|null $route
 * @property int $order
 * @property bool $is_hidden
 * @property int $created_by
 * @property int $modified_by
 * @property int $sub_module_id
 * @property int|null $parent_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Menu> $children
 * @property-read int|null $children_count
 * @property-read \App\Models\User|null $createdBy
 * @property-read \App\Models\User|null $modifiedBy
 * @property-read Menu|null $parent
 * @property-read \App\Models\Web\Shared\SubModule $subModule
 * @property-read int|null $workflows_count
 * @method static \Database\Factories\Shared\MenuFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereIsHidden($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereSubModuleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Menu whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Menu extends Model
{
    use HasFactory;

    // protected $table = 'menus';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'icon',
        'route',
        'order',
        'is_hidden',
        'created_by',
        'modified_by',
        'sub_module_id',
        'parent_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'is_hidden' => 'boolean',
            'created_by' => 'integer',
            'modified_by' => 'integer',
            'sub_module_id' => 'integer',
            'parent_id' => 'integer',
        ];
    }

    public function parent(): HasOne
    {
        return $this->hasOne(Menu::class, 'parent_id', 'id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Menu::class, 'parent_id', 'id');
    }

    public function subModule(): BelongsTo
    {
        return $this->belongsTo(SubModule::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }
}
