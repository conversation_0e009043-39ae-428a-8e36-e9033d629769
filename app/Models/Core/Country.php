<?php

namespace App\Models\Core;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'iso2',
        'name',
        'status',
        'phone_code',
        'iso3',
        'native',
        'region',
        'subregion',
        'latitude',
        'longitude',
        'emoji',
        'emojiU',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'status' => 'boolean',
    ];

	public function states(): HasMany
	{
		return $this->hasMany(State::class, 'country_id', 'id');
	}

	public function cities(): HasMany
	{
		return $this->hasMany(City::class, 'country_id', 'id');
	}

    public function lgas(): HasMany
	{
		return $this->hasMany(Lga::class, 'country_id', 'id');
	}

	public function timezones(): HasMany
	{
		return $this->hasMany(Timezone::class, 'country_id', 'id');
	}

	/**
	 * @return HasOne
	 */
	public function currency(): HasOne
	{
		return $this->hasOne(Currency::class, 'country_id', 'id');
	}
}
