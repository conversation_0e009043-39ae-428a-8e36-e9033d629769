<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StakeholderCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function subtypes()
    {
        return $this->hasMany(CategorySubtype::class);
    }

    public function applications()
    {
        return $this->hasMany(AccreditationApplication::class);
    }

    public function activeSubtypes()
    {
        return $this->subtypes()->where('is_active', true)->orderBy('sort_order');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
