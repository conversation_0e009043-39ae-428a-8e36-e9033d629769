<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Document extends Model
{
    use HasFactory;

    protected $table = 'accreditation_documents';

    protected $fillable = [
        'accreditation_application_id',
        'document_type',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'verification_status',
        'is_required',
        'expiry_date',
        'admin_notes',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'expiry_date' => 'date',
        'file_size' => 'integer',
    ];

    /**
     * Get the application that owns the document
     */
    public function accreditationApplication(): BelongsTo
    {
        return $this->belongsTo(AccreditationApplication::class);
    }

    /**
     * Scope for required documents
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for verified documents
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope for pending documents
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    /**
     * Scope for rejected documents
     */
    public function scopeRejected($query)
    {
        return $query->where('verification_status', 'rejected');
    }
}
