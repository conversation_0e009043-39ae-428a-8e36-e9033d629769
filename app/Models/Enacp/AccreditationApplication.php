<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AccreditationApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stakeholder_category_id',
        'category_subtype_id',
        'application_number',
        'status',
        'status_updated_at',
        'fee_paid',
        'payment_status',
        'fee_amount',
        'application_data',
        'submitted_at',
        'admin_notes',
        'rejection_reason',
        'reviewed_at',
        'reviewed_by',
    ];



    protected $casts = [
        'submitted_at' => 'date',
        'approved_at' => 'date',
        'expires_at' => 'date',
        'status_updated_at' => 'datetime',
        'fee_paid' => 'decimal:2',
        'application_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(StakeholderCategory::class, 'stakeholder_category_id');
    }

    /**
     * Get the subtype for this application
     */
    public function subtype(): BelongsTo
    {
        return $this->belongsTo(CategorySubtype::class, 'category_subtype_id');
    }

    /**
     * Get the documents for this application
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the certificate for this application
     */
    public function certificate()
    {
        return $this->hasOne(Certificate::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }


}
