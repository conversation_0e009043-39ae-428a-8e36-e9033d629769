<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_name',
        'rc_number',
        'tax_id',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'website',
        'position',
        'business_type',
        'years_in_business',
        'annual_revenue',
        'number_of_employees',
        'business_description',
        'category_subtype_id',
        'payment_reference',
        'created_by',
        'modified_by',
    ];

    protected $casts = [
        'years_in_business' => 'integer',
        'annual_revenue' => 'float:2',
        'number_of_employees' => 'integer',
        'created_by' => 'integer',
        'modified_by' => 'integer',
    ];

    /**
     * Get the user who created the asset.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last modified the asset.
     */
    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }

    /**
     * Get the category subtype that owns the business profile
     */
    public function categorySubtype(): BelongsTo
    {
        return $this->belongsTo(CategorySubtype::class);
    }

    /**
     * Get the formatted annual revenue
     */
    public function getFormattedAnnualRevenueAttribute(): string
    {
        return 'NGN ' . number_format($this->annual_revenue, 2);
    }

    /**
     * Scope for active business profiles
     */
    public function scopeActive($query)
    {
        return $query->whereNotNull('company_name');
    }

    /**
     * Scope for business profiles by business type
     */
    public function scopeByBusinessType($query, string $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * Scope for business profiles by country
     */
    public function scopeByCountry($query, string $country)
    {
        return $query->where('country', $country);
    }
}
