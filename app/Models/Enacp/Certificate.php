<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Certificate extends Model
{
    use HasFactory;

    protected $table = 'accreditation_certificates';

    protected $fillable = [
        'accreditation_application_id',
        'certificate_number',
        'qr_code_path',
        'certificate_path',
        'certificate_image_path',
        'issued_date',
        'expiry_date',
        'status',
        'certificate_data',
    ];

    protected $appends = ['qr_code_url', 'certificate_url', 'certificate_image_url'];

    protected $casts = [
        'issued_date' => 'date',
        'expiry_date' => 'date',
        'certificate_data' => 'array',
    ];

    /**
     * Get the accreditation application that owns this certificate
     */
    public function accreditationApplication(): BelongsTo
    {
        return $this->belongsTo(AccreditationApplication::class);
    }

    /**
     * Get the user through the accreditation application
     */
    public function user()
    {
        return $this->accreditationApplication->user;
    }

    /**
     * Get QR code URL
     */
    public function getQrCodeUrlAttribute(): ?string
    {
        if ($this->qr_code_path) {
            return Storage::disk('public')->url($this->qr_code_path);
        }

        return null;
    }

    /**
     * Get certificate PDF URL
     */
    public function getCertificateUrlAttribute(): ?string
    {
        if ($this->certificate_path) {
            return Storage::disk('public')->url($this->certificate_path);
        }

        return null;
    }

    /**
     * Get certificate image URL
     */
    public function getCertificateImageUrlAttribute(): ?string
    {
        if ($this->certificate_image_path) {
            return Storage::disk('public')->url($this->certificate_image_path);
        }

        return null;
    }

    /**
     * Scope for active certificates
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->where('expiry_date', '>', now());
    }

    /**
     * Scope for expired certificates
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }
}
