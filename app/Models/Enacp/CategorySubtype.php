<?php

declare(strict_types=1);

namespace App\Models\Enacp;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CategorySubtype extends Model
{
    use HasFactory;

    protected $fillable = [
        'stakeholder_category_id',
        'name',
        'slug',
        'accreditation_fee',
        'renewal_fee',
        'currency',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'accreditation_fee' => 'decimal:2',
        'renewal_fee' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function category()
    {
        return $this->belongsTo(StakeholderCategory::class, 'stakeholder_category_id');
    }

    public function applications()
    {
        return $this->hasMany(AccreditationApplication::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
