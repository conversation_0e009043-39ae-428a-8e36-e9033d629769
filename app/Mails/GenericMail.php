<?php

declare(strict_types=1);

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class GenericMail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        protected string $subject,
        protected string $greetings = 'Dear user',
        protected array $paragraphs = [],
        protected array $tableHeaders = [],
        protected array $tableRows = []
    ) {
        $this->afterCommit();
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): Mailable
    {
        return $this->from(config('mail.from.address'), config('mail.from.address'))
            ->subject(config('app.name') . " - {$this->subject}")
            ->view('mails.generic', [
                'greetings' => $this->greetings,
                'paragraphs' => $this->paragraphs,
                'tableHeaders' => $this->tableHeaders,
                'tableRows' => $this->tableRows,
            ]);
    }
}
