<?php

declare(strict_types=1);

namespace App\Mails\Enacp;

use App\Models\Enacp\AccreditationApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ApplicationStatusEmail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $application;

    public $user;

    public $status;

    public $reason;

    /**
     * Create a new message instance.
     */
    public function __construct(AccreditationApplication $application, $status, $reason = null)
    {
        $this->application = $application;
        $this->user = $application->user;
        $this->status = $status;
        $this->reason = $reason;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->status === 'approved'
            ? 'Application Approved - ELRA Accreditation'
            : 'Application Update - ELRA Accreditation';

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->status === 'approved'
            ? 'emails.enacp.application-approved'
            : 'emails.enacp.application-rejected';

        return new Content(
            view: $view,
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
