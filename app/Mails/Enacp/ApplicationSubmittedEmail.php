<?php

declare(strict_types=1);

namespace App\Mails\Enacp;

use App\Models\Enacp\AccreditationApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ApplicationSubmittedEmail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $application;

    public $user;

    /**
     * Create a new message instance.
     */
    public function __construct(AccreditationApplication $application)
    {
        $this->application = $application;
        $this->user = $application->user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Application Submitted - ELRA Accreditation',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.enacp.application-submitted',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
