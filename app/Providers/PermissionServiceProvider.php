<?php

namespace App\Providers;

use Illuminate\Filesystem\Filesystem;
use Illuminate\Routing\Route;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Illuminate\View\Compilers\BladeCompiler;

class PermissionServiceProvider extends ServiceProvider
{
    public function boot()
    {
        //
    }

    public function register()
    {
        $this->callAfterResolving('blade.compiler', function (BladeCompiler $bladeCompiler) {
            $this->registerBladeExtensions($bladeCompiler);
        });
    }

    public static function bladeMethodWrapper($method, $role, $guard = null)
    {
        return auth($guard)->check() && auth($guard)->user()->{$method}($role);
    }

    protected function registerBladeExtensions($bladeCompiler)
    {
        $bladeCompiler->directive('role', function ($arguments) {
            return "<?php if(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasRole', {$arguments})): ?>";
        });
        $bladeCompiler->directive('elserole', function ($arguments) {
            return "<?php elseif(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasRole', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endrole', function () {
            return '<?php endif; ?>';
        });

        $bladeCompiler->directive('hasrole', function ($arguments) {
            return "<?php if(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasRole', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endhasrole', function () {
            return '<?php endif; ?>';
        });

        $bladeCompiler->directive('hasanyrole', function ($arguments) {
            return "<?php if(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasAnyRole', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endhasanyrole', function () {
            return '<?php endif; ?>';
        });

        $bladeCompiler->directive('hasallroles', function ($arguments) {
            return "<?php if(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasAllRoles', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endhasallroles', function () {
            return '<?php endif; ?>';
        });

        $bladeCompiler->directive('unlessrole', function ($arguments) {
            return "<?php if(! \\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasRole', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endunlessrole', function () {
            return '<?php endif; ?>';
        });

        $bladeCompiler->directive('hasexactroles', function ($arguments) {
            return "<?php if(\\App\\Providers\\PermissionServiceProvider::bladeMethodWrapper('hasExactRoles', {$arguments})): ?>";
        });
        $bladeCompiler->directive('endhasexactroles', function () {
            return '<?php endif; ?>';
        });
    }
}
