<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupAvatarDefault extends Command
{
    protected $signature = 'setup:avatar';

    protected $description = 'Create default avatar folder, copy avatar_default.jpg, and link storage';

    public function handle()
    {
        $targetDir = storage_path('app/public/files/avatar');

        // Create directory if missing
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
            $this->info("Directory created: $targetDir");
        }

        // Copy file
        $sourceFile = public_path('assets/avatar_default.jpg');
        $targetFile = $targetDir . DIRECTORY_SEPARATOR . 'avatar_default.jpg';

        if (!file_exists($sourceFile)) {
            $this->error("Source file not found: $sourceFile");
            return;
        }

        if (!copy($sourceFile, $targetFile)) {
            $this->error("Failed to copy avatar.");
            return;
        }

        $this->info("Avatar copied to: $targetFile");

        // Create storage symlink
        Artisan::call('storage:link');
        $this->info("Storage link created.");

        $this->info("✅ Setup complete!");
    }
}
