<?php

namespace App\Services;

use App\Mails\GenericMail;
use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class MailService
{
    protected $user;

    public function __construct()
    {
        $this->user = auth()->user();
    }

    public function sendMailToRoles(array $roleSlugs, string $title, string $message = null, array $data = []): bool
    {
        $users = User::whereHas('roles', function ($query) use ($roleSlugs) {
            $query->whereIn('slug', $roleSlugs);
        })->get();
        if ($users->isEmpty()) {
            return false;
        }

        $mail = new GenericMail($title, $message);
        foreach ($users as $user) {
            Mail::to($user->email)->send($mail);
        }

        return true;
    }

    public function sendMailToUsers(array $userIds, string $title, string $message = null, array $data = []): bool
    {
        $users = User::whereIn('id', $userIds)->get();
        if ($users->isEmpty()) {
            return false;
        }

        $mail = new GenericMail($title, $message);
        foreach ($users as $user) {
            Mail::to($user->email)->send($mail);
        }

        return true;
    }

    public function sendMail(array $targets, string $title, string $message = null, array $data = []): bool
    {
        $success = false;

        foreach ($targets as $targetType => $targetValues) {
            switch ($targetType) {
                case 'roles':
                    $success = $this->sendMailToRoles($targetValues, $title, $message, $data) || $success;
                    break;
                case 'users':
                    $success = $this->sendMailToUsers($targetValues, $title, $message, $data) || $success;
                    break;
            }
        }

        return $success;
    }

    public function sendToUser(int $userId, string $title, string $message = null, array $data = []): bool
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        $mail = new GenericMail($title, $message);
        Mail::to($user->email)->send($mail);

        return true;
    }

    public function sendCustomMail(User $user, Mailable $mailable): bool
    {
        Mail::to($user->email)->send($mailable);
        return true;
    }
}
