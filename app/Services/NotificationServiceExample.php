<?php

namespace App\Services;

/**
 * Example usage of NotificationService
 * This file demonstrates how to use the NotificationService in various scenarios
 */
class NotificationServiceExample
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Example: Send notification to admin roles
     */
    public function notifyAdmins()
    {
        return $this->notificationService->sendToRoles(
            ['admin', 'super-admin'],
            'System Alert',
            'A new user has registered and requires approval.',
            ['type' => 'user_registration', 'priority' => 'high']
        );
    }

    /**
     * Example: Send notification to specific users
     */
    public function notifySpecificUsers()
    {
        $userIds = [1, 5, 10];

        return $this->notificationService->sendToUsers(
            $userIds,
            'Personal Message',
            'You have been selected for the special project.',
            ['project_id' => 123, 'type' => 'assignment']
        );
    }

    /**
     * Example: Send notification using the generic send method
     */
    public function sendComplexNotification()
    {
        $targets = [
            'roles' => ['admin', 'manager'],
            'users' => [5, 10]
        ];

        return $this->notificationService->send(
            $targets,
            'System Maintenance',
            'The system will be under maintenance this weekend.',
            [
                'maintenance_start' => now()->addDays(5),
                'maintenance_end' => now()->addDays(6),
                'type' => 'maintenance',
                'action_url' => url('/maintenance-info'),
                'action_text' => 'View Details'
            ]
        );
    }

    /**
     * Example: Get notification statistics
     */
    public function getNotificationStats()
    {
        return [
            'total_notifications' => $this->notificationService->notifications()->count(),
            'unread_notifications' => $this->notificationService->unreadNotificationsCount(),
            'recent_notifications' => $this->notificationService->notifications()->take(5)
        ];
    }

    /**
     * Example: Mark notifications as read
     */
    public function manageNotifications($notificationId = null)
    {
        if ($notificationId) {
            // Mark specific notification as read
            return $this->notificationService->markAsRead($notificationId);
        } else {
            // Mark all notifications as read
            return $this->notificationService->markAllAsRead();
        }
    }
}
