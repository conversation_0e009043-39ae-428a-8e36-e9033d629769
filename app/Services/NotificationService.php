<?php

namespace App\Services;

use App\Models\User;
use App\Notifications\GenericNotification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class NotificationService
{
    protected $user;

    public function __construct()
    {
        $this->user = auth()->user();
    }

    public function unreadNotificationsCount(): int
    {
        return $this->user ? $this->user->unreadNotifications()->count() : 0;
    }

    public function unreadNotifications()
    {
        return $this->user ? $this->user->unreadNotifications() : collect();
    }

    public function markAllAsRead(): bool
    {
        if (!$this->user) {
            return false;
        }

        $this->user->unreadNotifications()->markAsRead();
        return true;
    }

    public function markAsRead(string $notificationId): bool
    {
        if (!$this->user) {
            return false;
        }

        $notification = $this->user->notifications()->find($notificationId);

        if ($notification && is_null($notification->read_at)) {
            $notification->markAsRead();
            return true;
        }

        return false;
    }

    public function notifications()
    {
        return $this->user ? $this->user->notifications : collect();
    }

    public function sendToRoles(array $roleSlugs, string $title, string $message = null, array $data = []): bool
    {
        $users = User::whereHas('roles', function ($query) use ($roleSlugs) {
            $query->whereIn('slug', $roleSlugs);
        })->get();

        if ($users->isEmpty()) {
            return false;
        }

        $notification = new GenericNotification($title, $message, $data);
        Notification::sendNow($users, $notification, ['database']);

        return true;
    }

    public function send(array $targets, string $title, string $message = null, array $data = []): bool
    {
        $success = false;

        foreach ($targets as $targetType => $targetValues) {
            switch ($targetType) {
                case 'roles':
                    $success = $this->sendToRoles($targetValues, $title, $message, $data) || $success;
                    break;
                case 'users':
                    $success = $this->sendToUsers($targetValues, $title, $message, $data) || $success;
                    break;
            }
        }

        return $success;
    }

    public function sendToUsers(array $userIds, string $title, string $message = null, array $data = []): bool
    {
        $users = User::whereIn('id', $userIds)->get();

        if ($users->isEmpty()) {
            return false;
        }

        $notification = new GenericNotification($title, $message, $data);
        Notification::sendNow($users, $notification, ['database']);

        return true;
    }

    public function sendToUser(int $userId, string $title, string $message = null, array $data = []): bool
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        $notification = new GenericNotification($title, $message, $data);
        Notification::sendNow([$user], $notification, ['database']);

        return true;
    }

    public function getUsersByRoles(array $roleSlugs): Collection
    {
        return User::whereHas('roles', function ($query) use ($roleSlugs) {
            $query->whereIn('slug', $roleSlugs);
        })->get();
    }
}
