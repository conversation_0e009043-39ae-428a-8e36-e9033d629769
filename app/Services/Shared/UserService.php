<?php

namespace App\Services\Shared;

use App\DTOs\Enacp\RegistrationUserDTO;
use App\DTOs\UserUpdateDTO;
use App\Models\Web\AccessControl\Role;
use App\Models\User;
use App\Repositories\Shared\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserService
{
    public function __construct(protected UserRepository $repo)
    {
    }

    public function create(array $data): User
    {
        $data['password'] = Hash::make($data['password']);

        return $this->repo->create($data);
    }

    /**
     * Create a user from DTO
     */
    public function createFromDTO(RegistrationUserDTO $dto): User
    {
        return $this->repo->create($dto->toArray());
    }

    public function getAllUsers(): Collection
    {
        return $this->repo->getAllUsers();
    }

    public function update(User $user, array $data): User
    {
        // if (isset($data['password']) && $data['password']) {
        //     $data['password'] = Hash::make($data['password']);
        // } else {
        //     unset($data['password']);
        // }
        $updatedUser = $this->repo->update($user, $data);
        // Sync role if provided
        if (array_key_exists('role_id', $data) && $data['role_id']) {
            $this->syncRoles($updatedUser, [$data['role_id']]);
        }
        return $updatedUser;
    }

    public function delete(User $user): bool
    {
        return $this->repo->delete($user);
    }

    public function getUserById(int $id): User
    {
        return $this->repo->getUserById($id);
    }

    public function getUserByEmail(string $email): User
    {
        return $this->repo->getUserByEmail($email);
    }

    public function getUserByPhoneNo(string $phone): User
    {
        return $this->repo->getUserByPhoneNo($phone);
    }

    public function getUserByIdentifier(string $identifier): User
    {
        return $this->repo->getUserByIdentifier($identifier);
    }

    /**
     * @param User $user
     * @param array|Collection|int|Role|string $roleIds
     */
    public function syncRoles($user, $roleIds): array
    {
        return $user->roles()->sync($roleIds);
    }

    /**
     * Find user by ID
     */
    public function findById(int $id): ?User
    {
        return $this->repo->findById($id);
    }

    /**
     * Find user by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = []): ?User
    {
        return $this->repo->findByIdWithRelations($id, $relations);
    }

    /**
     * Get all users with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        return $this->repo->getAllWithRelations($relations);
    }

    /**
     * Update user status
     */
    public function updateStatus(User $user, string $status, ?string $adminNotes = null): User
    {
        $dto = UserUpdateDTO::fromArray([
            'status' => $status,
            'admin_notes' => $adminNotes,
        ]);

        return $this->repo->update($user, $dto->toArray());
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        return $this->repo->emailExists($email);
    }

    /**
     * Update user profile
     */
    public function updateProfile(User $user, array $data): User
    {
        $profilePicturePath = null;

        // Handle profile picture upload
        if (isset($data['profile_picture']) && $data['profile_picture'] instanceof UploadedFile) {
            // Delete old profile picture
            if ($user->profile_picture) {
                Storage::disk('public')->delete($user->profile_picture);
            }

            $profilePicturePath = $data['profile_picture']->store('profile-pictures', 'public');
        }

        // Prepare update data
        $updateData = collect($data)->except(['profile_picture'])->toArray();
        if ($profilePicturePath) {
            $updateData['profile_picture'] = $profilePicturePath;
        }

        $dto = UserUpdateDTO::fromArray($updateData);

        return $this->repo->update($user, $dto->toArray());
    }

    /**
     * Change user password
     */
    public function changePassword(User $user, string $currentPassword, string $newPassword): void
    {
        // Check current password
        if (!Hash::check($currentPassword, $user->password)) {
            throw new \InvalidArgumentException('Current password is incorrect');
        }

        $dto = UserUpdateDTO::fromArray([
            'password' => Hash::make($newPassword),
        ]);

        $this->repo->update($user, $dto->toArray());
    }
}
