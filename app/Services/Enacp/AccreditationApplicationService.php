<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\AccreditationApplicationDTO;
use App\DTOs\Enacp\AccreditationApplicationUpdateDTO;
use App\Models\Enacp\AccreditationApplication;
use App\Repositories\Enacp\AccreditationApplicationRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class AccreditationApplicationService
{
    public function __construct(
        protected AccreditationApplicationRepository $repository
    ) {
    }

    /**
     * Create a new accreditation application
     */
    public function create(AccreditationApplicationDTO $dto): AccreditationApplication
    {
        $data = $dto->toArray();

        // Generate application number if not provided
        if (empty($data['application_number'])) {
            $data['application_number'] = $this->repository->getNextApplicationNumber();
        }

        return $this->repository->create($data);
    }

    /**
     * Update an existing accreditation application
     */
    public function update(
        AccreditationApplication $application,
        AccreditationApplicationUpdateDTO $dto
    ): AccreditationApplication {
        $data = $dto->toArray();

        // Handle status changes
        if (isset($data['status']) && $data['status'] !== $application->status) {
            $data['status_updated_at'] = now();

            if ($data['status'] === 'submitted' && !$data['submitted_at']) {
                $data['submitted_at'] = now();
            }

            if (in_array($data['status'], ['approved', 'rejected']) && isset($data['reviewed_by'])) {
                $data['reviewed_at'] = now();
            }
        }

        return $this->repository->update($application, $data);
    }

    /**
     * Delete an accreditation application
     */
    public function delete(AccreditationApplication $application): bool
    {
        // Check if application can be deleted
        if ($application->status === 'approved' && $application->certificate) {
            throw new \InvalidArgumentException('Cannot delete approved application with certificate.');
        }

        return $this->repository->delete($application);
    }

    /**
     * Find an application by ID
     */
    public function findById(int $id): ?AccreditationApplication
    {
        return $this->repository->findById($id);
    }

    /**
     * Find an application by ID with relationships
     */
    public function findByIdWithRelations(
        int $id,
        array $relations = ['user', 'category', 'subtype', 'documents', 'certificate']
    ): ?AccreditationApplication {
        return $this->repository->findByIdWithRelations($id, $relations);
    }

    /**
     * Find an application by application number
     */
    public function findByApplicationNumber(string $applicationNumber): ?AccreditationApplication
    {
        return $this->repository->findByApplicationNumber($applicationNumber);
    }

    /**
     * Get paginated applications
     */
    public function getPaginated(
        int $perPage = 15,
        array $relations = ['user', 'category', 'subtype']
    ): LengthAwarePaginator {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Get applications by user ID
     */
    public function getByUserId(int $userId, array $relations = ['category', 'subtype', 'documents']): Collection
    {
        return $this->repository->getByUserIdWithRelations($userId, $relations);
    }

    /**
     * Get all applications with relationships
     */
    public function getAllWithRelations(array $relations = []): Collection
    {
        return $this->repository->getAllWithRelations($relations);
    }

    /**
     * Find application by application number with relationships
     */
    public function findByApplicationNumberWithRelations(
        string $applicationNumber,
        array $relations = []
    ): ?AccreditationApplication {
        return $this->repository->findByApplicationNumberWithRelations($applicationNumber, $relations);
    }

    /**
     * Find approved application by ID with relationships
     */
    public function findApprovedByIdWithRelations(int $id, array $relations = []): ?AccreditationApplication
    {
        return $this->repository->findApprovedByIdWithRelations($id, $relations);
    }

    /**
     * Find user's draft application by ID
     */
    public function findUserDraftApplication(int $userId, int $applicationId): ?AccreditationApplication
    {
        return $this->repository->findUserDraftApplication($userId, $applicationId);
    }

    /**
     * Get applications by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->repository->getByStatus($status);
    }

    /**
     * Search applications
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->search($criteria, $perPage);
    }

    /**
     * Submit application for review
     */
    public function submit(AccreditationApplication $application): AccreditationApplication
    {
        if ($application->status !== 'draft') {
            throw new \InvalidArgumentException('Only draft applications can be submitted.');
        }

        // Validate required documents
        $this->validateRequiredDocuments($application);

        $dto = AccreditationApplicationUpdateDTO::fromArray([
            'status' => 'submitted',
            'submitted_at' => now()->toDateString(),
        ]);

        return $this->update($application, $dto);
    }

    /**
     * Approve application
     */
    public function approve(
        AccreditationApplication $application,
        int $reviewedBy,
        ?string $adminNotes = null
    ): AccreditationApplication {
        if (!in_array($application->status, ['submitted', 'under_review'])) {
            throw new \InvalidArgumentException('Only submitted or under review applications can be approved.');
        }

        $dto = AccreditationApplicationUpdateDTO::fromArray([
            'status' => 'approved',
            'reviewed_by' => $reviewedBy,
            'admin_notes' => $adminNotes,
        ]);

        return $this->update($application, $dto);
    }

    /**
     * Reject application
     */
    public function reject(
        AccreditationApplication $application,
        int $reviewedBy,
        string $rejectionReason,
        ?string $adminNotes = null
    ): AccreditationApplication {
        if (!in_array($application->status, ['submitted', 'under_review'])) {
            throw new \InvalidArgumentException('Only submitted or under review applications can be rejected.');
        }

        $dto = AccreditationApplicationUpdateDTO::fromArray([
            'status' => 'rejected',
            'reviewed_by' => $reviewedBy,
            'rejection_reason' => $rejectionReason,
            'admin_notes' => $adminNotes,
        ]);

        return $this->update($application, $dto);
    }

    /**
     * Mark application as under review
     */
    public function markUnderReview(AccreditationApplication $application, int $reviewedBy): AccreditationApplication
    {
        if ($application->status !== 'submitted') {
            throw new \InvalidArgumentException('Only submitted applications can be marked as under review.');
        }

        $dto = AccreditationApplicationUpdateDTO::fromArray([
            'status' => 'under_review',
            'reviewed_by' => $reviewedBy,
        ]);

        return $this->update($application, $dto);
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(
        AccreditationApplication $application,
        string $paymentStatus,
        ?float $feePaid = null,
        ?string $paymentReference = null
    ): AccreditationApplication {
        $data = ['payment_status' => $paymentStatus];

        if ($feePaid !== null) {
            $data['fee_paid'] = $feePaid;
        }

        if ($paymentReference !== null) {
            $data['payment_reference'] = $paymentReference;
        }

        $dto = AccreditationApplicationUpdateDTO::fromArray($data);
        return $this->update($application, $dto);
    }

    /**
     * Get applications statistics
     */
    public function getStatistics(): array
    {
        return $this->repository->getStatistics();
    }

    /**
     * Get applications pending review
     */
    public function getPendingReview(): Collection
    {
        return $this->repository->getPendingReview();
    }

    /**
     * Get applications with certificates expiring soon
     */
    public function getWithCertificatesExpiringSoon(int $days = 30): Collection
    {
        return $this->repository->getWithCertificatesExpiringSoon($days);
    }

    /**
     * Find application by ID and user ID
     */
    public function findByIdAndUserId(int $id, int $userId, array $relations = []): ?AccreditationApplication
    {
        return $this->repository->findByIdAndUserId($id, $userId, $relations);
    }

    /**
     * Find draft application by ID and user ID
     */
    public function findDraftByIdAndUserId(int $id, int $userId, array $relations = []): ?AccreditationApplication
    {
        return $this->repository->findDraftByIdAndUserId($id, $userId, $relations);
    }

    /**
     * Check if user has pending application for category
     */
    public function userHasPendingApplicationForCategory(int $userId, int $categoryId): bool
    {
        return $this->repository->userHasPendingApplicationForCategory($userId, $categoryId);
    }

    /**
     * Validate required documents for application
     */
    protected function validateRequiredDocuments(AccreditationApplication $application): void
    {
        $requiredDocuments = $this->getRequiredDocuments($application->category->name ?? '');
        $uploadedDocuments = $application->documents->pluck('document_type')->toArray();
        $missingDocuments = array_diff($requiredDocuments, $uploadedDocuments);

        if (!empty($missingDocuments)) {
            throw new \InvalidArgumentException(
                'Please upload all required documents before submitting. Missing: ' .
                implode(', ', $missingDocuments)
            );
        }

        // Check if all uploaded documents are verified
        $pendingDocuments = $application->documents->where('verification_status', 'pending')->count();
        if ($pendingDocuments > 0) {
            throw new \InvalidArgumentException(
                'Some documents are still pending verification. Please wait for admin review.'
            );
        }
    }

    /**
     * Get required documents for category
     */
    protected function getRequiredDocuments(string $categoryName): array
    {
        $requiredDocuments = [
            'lessor' => ['cac_certificate', 'tax_clearance', 'bank_statement', 'financial_statement'],
            'lessee' => ['cac_certificate', 'tax_clearance', 'bank_statement'],
            'practitioners' => ['professional_certificate', 'cv_resume', 'reference_letter'],
        ];

        $category = strtolower($categoryName);
        return $requiredDocuments[$category] ?? [];
    }

    /**
     * Generate application number (moved from model)
     */
    public function generateApplicationNumber(): string
    {
        $prefix = 'ENACP';
        $year = date('Y');
        $lastApplication = AccreditationApplication::whereYear('created_at', $year)->latest()->first();

        if ($lastApplication) {
            $lastNumber = (int) substr($lastApplication->application_number, -6);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad((string) $newNumber, 6, '0', STR_PAD_LEFT);
    }
}
