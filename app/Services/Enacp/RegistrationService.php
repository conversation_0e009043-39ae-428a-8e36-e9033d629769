<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\BusinessProfileDTO;
use App\DTOs\Enacp\RegistrationUserDTO;
use App\Mails\Enacp\WelcomeEmail;
use App\Models\Enacp\AccreditationApplication;
use App\Models\Enacp\CategorySubtype;
use App\Models\User;
use App\Services\Enacp\BusinessProfileService;
use App\Services\Shared\UserService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class RegistrationService
{
    public function __construct(
        protected UserService $userService,
        protected BusinessProfileService $businessProfileService,
        protected AccreditationApplicationService $applicationService
    ) {
    }

    /**
     * Register a new user with application
     */
    public function registerUser(array $data): array
    {
        $profilePicturePath = null;

        try {
            // Handle profile picture upload
            if (isset($data['profile_picture'])) {
                $profilePicturePath = $data['profile_picture']->store('profile-pictures', 'public');
            }

            // Create user using UserService and DTO
            $userDTO = RegistrationUserDTO::fromArray([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'password' => Hash::make($data['password']),
                'profile_picture' => $profilePicturePath,
                'status' => 'pending',
                'role' => 'user',
            ]);

            $user = $this->userService->createFromDTO($userDTO);

            // Generate email verification token
            $verificationToken = $user->generateEmailVerificationToken();

            // Create business profile using BusinessProfileService and DTO
            $businessProfileDTO = BusinessProfileDTO::fromArray([
                'company_name' => $data['company_name'],
                'rc_number' => $data['rc_number'] ?? null,
                'tax_id' => $data['tax_id'] ?? null,
                'address' => $data['address'],
                'city' => $data['city'],
                'state' => $data['state'],
                'country' => $data['country'],
                'postal_code' => $data['postal_code'] ?? null,
                'website' => $data['website'] ?? null,
                'position' => $data['position'],
                'business_type' => $data['business_type'],
                'years_in_business' => $data['years_in_business'],
                'annual_revenue' => $data['annual_revenue'],
                'number_of_employees' => $data['number_of_employees'],
                'business_description' => $data['business_description'],
                'category_subtype_id' => $data['category_subtype_id'],
                'payment_reference' => $data['payment_reference'] ?? null,
            ]);

            $businessProfile = $this->businessProfileService->createForUser($user, $businessProfileDTO);

            // Create accreditation application using ApplicationService
            $subtype = CategorySubtype::with('category')->findOrFail($data['category_subtype_id']);

            // Validate that the category relationship exists
            if (!$subtype->category) {
                throw new \InvalidArgumentException('Invalid category subtype: associated category not found');
            }

            $applicationData = collect($data)->except([
                'password',
                'password_confirmation',
                'profile_picture',
                'payment_reference'
            ])->toArray();

            $application = AccreditationApplication::create([
                'user_id' => $user->id,
                'stakeholder_category_id' => $subtype->stakeholder_category_id,
                'category_subtype_id' => $subtype->id,
                'status' => 'draft',
                'application_data' => $applicationData,
                'fee_paid' => true,
                'payment_status' => 'paid',
                'fee_amount' => $subtype->accreditation_fee,
                'payment_reference' => $data['payment_reference'] ?? null,
            ]);

            // Send verification email
            $this->sendVerificationEmail($user, $verificationToken);

            // Generate token for immediate login
            $token = $user->createToken('auth_token')->plainTextToken;

            return [
                'user' => $user,
                'business_profile' => $businessProfile,
                'application' => $application->load(['category', 'subtype']),
                'token' => $token,
            ];
        } catch (\Exception $e) {
            // Clean up uploaded file if user creation fails
            if ($profilePicturePath && Storage::disk('public')->exists($profilePicturePath)) {
                Storage::disk('public')->delete($profilePicturePath);
            }

            throw $e;
        }
    }

    /**
     * Verify email address
     */
    public function verifyEmail(string $token): void
    {
        \Log::info('Verification attempt with token: ' . substr($token, 0, 10) . '...');

        $user = User::where('email_verification_token', $token)->first();

        if (!$user) {
            \Log::warning('No user found with token: ' . substr($token, 0, 10) . '...');
            throw new \InvalidArgumentException('Invalid verification token');
        }

        \Log::info("User found: {$user->email}, isVerified: " . ($user->isVerified() ? 'true' : 'false'));

        if ($user->isVerified()) {
            throw new \InvalidArgumentException('Email already verified');
        }

        $user->markEmailAsVerified();
        \Log::info("Email verified successfully for user: {$user->email}");
    }

    /**
     * Resend verification email
     */
    public function resendVerificationEmail(string $email): void
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            throw new \InvalidArgumentException('User not found');
        }

        if ($user->isVerified()) {
            throw new \InvalidArgumentException('Email already verified');
        }

        $verificationToken = $user->generateEmailVerificationToken();
        $this->sendVerificationEmail($user, $verificationToken);
    }

    /**
     * Send verification email
     */
    protected function sendVerificationEmail(User $user, string $token): void
    {
        $verificationUrl = "http://localhost:5173/verify-email?token={$token}&email=" . urlencode($user->email);

        try {
            // Send the welcome email with verification link
            Mail::to($user->email)->send(new WelcomeEmail($user, $verificationUrl));

            \Log::info("Welcome email sent successfully to {$user->email}");
        } catch (\Exception $e) {
            \Log::error("Failed to send welcome email to {$user->email}: " . $e->getMessage());

            // Still log the verification URL for development
            \Log::info("Verification URL for {$user->email}: {$verificationUrl}");
        }
    }
}
