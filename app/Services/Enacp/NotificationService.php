<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\Models\Enacp\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class NotificationService
{
    /**
     * Create a new notification
     */
    public function create(array $data): Notification
    {
        return Notification::create($data);
    }

    /**
     * Find a notification by ID
     */
    public function findById(int $id): ?Notification
    {
        return Notification::find($id);
    }

    /**
     * Get notifications for a user
     */
    public function getForUser(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Notification::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get unread notifications for a user
     */
    public function getUnreadForUser(int $userId): Collection
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get read notifications for a user
     */
    public function getReadForUser(int $userId): Collection
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Mark notification as read (moved from model)
     */
    public function markAsRead(Notification $notification): Notification
    {
        $notification->update([
            'is_read' => true,
            'read_at' => now(),
        ]);

        return $notification->refresh();
    }

    /**
     * Mark notification as unread (moved from model)
     */
    public function markAsUnread(Notification $notification): Notification
    {
        $notification->update([
            'is_read' => false,
            'read_at' => null,
        ]);

        return $notification->refresh();
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsReadForUser(int $userId): bool
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
    }

    /**
     * Get user notifications paginated
     */
    public function getUserNotificationsPaginated(int $userId, int $perPage = 20): LengthAwarePaginator
    {
        return Notification::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get user unread notifications
     */
    public function getUserUnreadNotifications(int $userId): Collection
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Find user notification by ID
     */
    public function findUserNotification(int $userId, int $notificationId): ?Notification
    {
        return Notification::where('user_id', $userId)
            ->where('id', $notificationId)
            ->first();
    }

    /**
     * Delete a notification
     */
    public function delete(Notification $notification): bool
    {
        return $notification->delete();
    }

    /**
     * Delete all notifications for a user
     */
    public function deleteAllForUser(int $userId): bool
    {
        return Notification::where('user_id', $userId)->delete();
    }

    /**
     * Delete read notifications for a user
     */
    public function deleteReadForUser(int $userId): bool
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', true)
            ->delete();
    }

    /**
     * Get notification count for a user
     */
    public function getCountForUser(int $userId): int
    {
        return Notification::where('user_id', $userId)->count();
    }

    /**
     * Get unread notification count for a user
     */
    public function getUnreadCountForUser(int $userId): int
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->count();
    }

    /**
     * Create application status notification
     */
    public function createApplicationStatusNotification(
        int $userId,
        string $applicationNumber,
        string $status,
        ?string $message = null
    ): Notification {
        $statusMessages = [
            'submitted' => 'Your application has been submitted successfully.',
            'under_review' => 'Your application is now under review.',
            'approved' => 'Congratulations! Your application has been approved.',
            'rejected' => 'Your application has been rejected.',
            'expired' => 'Your application has expired.',
        ];

        $title = "Application {$status}";
        $defaultMessage = $statusMessages[$status] ?? "Your application status has been updated to {$status}.";

        return $this->create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message ?? $defaultMessage,
            'type' => 'application_status',
            'data' => [
                'application_number' => $applicationNumber,
                'status' => $status,
            ],
        ]);
    }

    /**
     * Create document verification notification
     */
    public function createDocumentVerificationNotification(
        int $userId,
        string $documentType,
        string $status,
        ?string $notes = null
    ): Notification {
        $statusMessages = [
            'verified' => 'Your document has been verified successfully.',
            'rejected' => 'Your document has been rejected.',
            'pending' => 'Your document is pending verification.',
        ];

        $title = "Document {$status}";
        $message = $statusMessages[$status] ?? "Your document status has been updated.";

        if ($notes && $status === 'rejected') {
            $message .= " Reason: {$notes}";
        }

        return $this->create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'document_verification',
            'data' => [
                'document_type' => $documentType,
                'status' => $status,
                'notes' => $notes,
            ],
        ]);
    }

    /**
     * Create certificate notification
     */
    public function createCertificateNotification(
        int $userId,
        string $certificateNumber,
        string $type,
        ?string $message = null
    ): Notification {
        $typeMessages = [
            'issued' => 'Your certificate has been issued successfully.',
            'expiring' => 'Your certificate is expiring soon.',
            'expired' => 'Your certificate has expired.',
            'suspended' => 'Your certificate has been suspended.',
            'revoked' => 'Your certificate has been revoked.',
        ];

        $title = "Certificate {$type}";
        $defaultMessage = $typeMessages[$type] ?? "Certificate notification.";

        return $this->create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message ?? $defaultMessage,
            'type' => 'certificate',
            'data' => [
                'certificate_number' => $certificateNumber,
                'notification_type' => $type,
            ],
        ]);
    }
}
