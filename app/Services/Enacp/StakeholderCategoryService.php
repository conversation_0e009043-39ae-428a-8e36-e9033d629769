<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\StakeholderCategoryDTO;
use App\DTOs\Enacp\StakeholderCategoryUpdateDTO;
use App\Models\Enacp\StakeholderCategory;
use App\Repositories\Enacp\StakeholderCategoryRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class StakeholderCategoryService
{
    public function __construct(
        protected StakeholderCategoryRepository $repository
    ) {
    }

    /**
     * Create a new stakeholder category
     */
    public function create(StakeholderCategoryDTO $dto): StakeholderCategory
    {
        // Ensure unique slug
        $data = $dto->toArray();
        if ($this->repository->slugExists($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['slug']);
        }

        // Set sort order if not provided
        if ($data['sort_order'] === 0) {
            $data['sort_order'] = $this->repository->getNextSortOrder();
        }

        return $this->repository->create($data);
    }

    /**
     * Update an existing stakeholder category
     */
    public function update(StakeholderCategory $category, StakeholderCategoryUpdateDTO $dto): StakeholderCategory
    {
        $data = $dto->toArray();

        // Ensure unique slug if being updated
        if (isset($data['slug']) && $this->repository->slugExists($data['slug'], $category->id)) {
            $data['slug'] = $this->generateUniqueSlug($data['slug'], $category->id);
        }

        return $this->repository->update($category, $data);
    }

    /**
     * Delete a stakeholder category
     */
    public function delete(StakeholderCategory $category): bool
    {
        // Check if category has subtypes
        if ($category->subtypes()->exists()) {
            throw new \InvalidArgumentException('Cannot delete category with existing subtypes.');
        }

        // Check if category has applications
        if ($category->applications()->exists()) {
            throw new \InvalidArgumentException('Cannot delete category with existing applications.');
        }

        return $this->repository->delete($category);
    }

    /**
     * Find a stakeholder category by ID
     */
    public function findById(int $id): ?StakeholderCategory
    {
        return $this->repository->findById($id);
    }

    /**
     * Find a stakeholder category by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = ['subtypes']): ?StakeholderCategory
    {
        return $this->repository->findByIdWithRelations($id, $relations);
    }

    /**
     * Find a stakeholder category by slug
     */
    public function findBySlug(string $slug): ?StakeholderCategory
    {
        return $this->repository->findBySlug($slug);
    }

    /**
     * Get all stakeholder categories
     */
    public function getAll(): Collection
    {
        return $this->repository->getAll();
    }

    /**
     * Get all stakeholder categories with relationships
     */
    public function getAllWithRelations(array $relations = ['subtypes']): Collection
    {
        return $this->repository->getAllWithRelations($relations);
    }

    /**
     * Get paginated stakeholder categories
     */
    public function getPaginated(int $perPage = 15, array $relations = ['subtypes']): LengthAwarePaginator
    {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Get active stakeholder categories
     */
    public function getActive(): Collection
    {
        return $this->repository->getActive();
    }

    /**
     * Get active stakeholder categories with relationships
     */
    public function getActiveWithRelations(array $relations = ['activeSubtypes']): Collection
    {
        return $this->repository->getActiveWithRelations($relations);
    }

    /**
     * Get stakeholder categories ordered by sort order
     */
    public function getOrderedBySort(): Collection
    {
        return $this->repository->getOrderedBySort();
    }

    /**
     * Get active stakeholder categories ordered by sort order
     */
    public function getActiveOrderedBySort(): Collection
    {
        return $this->repository->getActiveOrderedBySort();
    }

    /**
     * Search stakeholder categories by name
     */
    public function searchByName(string $name): Collection
    {
        return $this->repository->searchByName($name);
    }

    /**
     * Get stakeholder categories with their active subtypes
     */
    public function getWithActiveSubtypes(): Collection
    {
        return $this->repository->getWithActiveSubtypes();
    }

    /**
     * Get stakeholder categories with subtype counts
     */
    public function getWithSubtypeCounts(): Collection
    {
        return $this->repository->getWithSubtypeCounts();
    }

    /**
     * Toggle category active status
     */
    public function toggleActive(StakeholderCategory $category): StakeholderCategory
    {
        $dto = StakeholderCategoryUpdateDTO::fromArray(['is_active' => !$category->is_active]);
        return $this->update($category, $dto);
    }

    /**
     * Reorder categories
     */
    public function reorder(array $categoryIds): bool
    {
        return $this->repository->reorder($categoryIds);
    }

    /**
     * Get category statistics
     */
    public function getStatistics(): array
    {
        $categories = $this->repository->getWithSubtypeCounts();

        return [
            'total_categories' => $categories->count(),
            'active_categories' => $categories->where('is_active', true)->count(),
            'total_subtypes' => $categories->sum('subtypes_count'),
            'categories_with_subtypes' => $categories->where('subtypes_count', '>', 0)->count(),
        ];
    }

    /**
     * Get active categories with their subtypes (alias for controller)
     */
    public function getActiveWithSubtypes(): Collection
    {
        return $this->getActiveWithRelations(['activeSubtypes']);
    }

    /**
     * Get all categories with their subtypes (alias for controller)
     */
    public function getAllWithSubtypes(): Collection
    {
        return $this->getAllWithRelations(['subtypes']);
    }

    /**
     * Find category by slug with subtypes
     */
    public function findBySlugWithSubtypes(string $slug): ?StakeholderCategory
    {
        return $this->repository->findBySlugWithRelations($slug, ['activeSubtypes']);
    }

    /**
     * Generate a unique slug
     */
    protected function generateUniqueSlug(string $baseSlug, ?int $excludeId = null): string
    {
        $slug = $baseSlug;
        $counter = 1;

        while ($this->repository->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
