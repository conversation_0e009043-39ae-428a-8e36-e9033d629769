<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\DocumentDTO;
use App\DTOs\Enacp\DocumentUpdateDTO;
use App\Models\Enacp\AccreditationApplication;
use App\Models\Enacp\Document;
use App\Repositories\Enacp\DocumentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentService
{
    public function __construct(
        protected DocumentRepository $repository
    ) {
    }

    /**
     * Create a new document
     */
    public function create(DocumentDTO $dto): Document
    {
        return $this->repository->create($dto->toArray());
    }

    /**
     * Create document from uploaded file
     */
    public function createFromUpload(
        int $applicationId,
        string $documentType,
        UploadedFile $file,
        bool $isRequired = true,
        ?string $expiryDate = null
    ): Document {
        // Store the file
        $filePath = $this->storeFile($file, $documentType);

        // Create DTO from uploaded file
        $dto = DocumentDTO::fromUploadedFile(
            $applicationId,
            $documentType,
            $file,
            $filePath,
            $isRequired,
            $expiryDate
        );

        return $this->create($dto);
    }

    /**
     * Update an existing document
     */
    public function update(Document $document, DocumentUpdateDTO $dto): Document
    {
        return $this->repository->update($document, $dto->toArray());
    }

    /**
     * Delete a document
     */
    public function delete(Document $document): bool
    {
        // Delete the physical file
        if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        return $this->repository->delete($document);
    }

    /**
     * Find a document by ID
     */
    public function findById(int $id): ?Document
    {
        return $this->repository->findById($id);
    }

    /**
     * Get documents by application ID
     */
    public function getByApplicationId(int $applicationId): Collection
    {
        return $this->repository->getByApplicationId($applicationId);
    }

    /**
     * Get documents by verification status
     */
    public function getByVerificationStatus(string $status): Collection
    {
        return $this->repository->getByVerificationStatus($status);
    }

    /**
     * Get paginated documents
     */
    public function getPaginated(
        int $perPage = 15,
        array $relations = ['accreditationApplication']
    ): LengthAwarePaginator {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Search documents
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->search($criteria, $perPage);
    }

    /**
     * Get documents statistics
     */
    public function getStatistics(): array
    {
        return $this->repository->getStatistics();
    }

    /**
     * Verify a document
     */
    public function verify(Document $document, ?string $notes = null): Document
    {
        $dto = DocumentUpdateDTO::fromArray([
            'verification_status' => 'verified',
            'verification_notes' => $notes,
        ]);

        return $this->update($document, $dto);
    }

    /**
     * Reject a document
     */
    public function reject(Document $document, string $reason): Document
    {
        $dto = DocumentUpdateDTO::fromArray([
            'verification_status' => 'rejected',
            'verification_notes' => $reason,
        ]);

        return $this->update($document, $dto);
    }

    /**
     * Mark document as pending
     */
    public function markAsPending(Document $document): Document
    {
        $dto = DocumentUpdateDTO::fromArray([
            'verification_status' => 'pending',
            'verification_notes' => null,
        ]);

        return $this->update($document, $dto);
    }

    /**
     * Get file URL (moved from model)
     */
    public function getFileUrl(Document $document): string
    {
        return Storage::url($document->file_path);
    }

    /**
     * Get formatted file size (moved from model)
     */
    public function getFormattedFileSize(Document $document): string
    {
        $bytes = $document->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if document is verified (moved from model)
     */
    public function isVerified(Document $document): bool
    {
        return $document->verification_status === 'verified';
    }

    /**
     * Check if document is pending (moved from model)
     */
    public function isPending(Document $document): bool
    {
        return $document->verification_status === 'pending';
    }

    /**
     * Check if document is rejected (moved from model)
     */
    public function isRejected(Document $document): bool
    {
        return $document->verification_status === 'rejected';
    }

    /**
     * Store uploaded file
     */
    protected function storeFile(UploadedFile $file, string $documentType): string
    {
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs("documents/{$documentType}", $filename, 'public');
    }

    /**
     * Get documents expiring soon
     */
    public function getExpiringSoon(int $days = 30): Collection
    {
        return $this->repository->getExpiringSoon($days);
    }

    /**
     * Get expired documents
     */
    public function getExpired(): Collection
    {
        return $this->repository->getExpired();
    }

    /**
     * Get required documents
     */
    public function getRequired(): Collection
    {
        return $this->repository->getRequired();
    }

    /**
     * Get optional documents
     */
    public function getOptional(): Collection
    {
        return $this->repository->getOptional();
    }

    /**
     * Upload document for application
     */
    public function uploadForApplication(
        AccreditationApplication $application,
        UploadedFile $file,
        string $documentType
    ): Document {
        // Check if document type already exists for this application
        $existingDocument = $this->repository->findByApplicationAndType($application->id, $documentType);

        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('documents', $fileName, 'public');

        if ($existingDocument) {
            // Delete old file
            if ($existingDocument->file_path && Storage::disk('public')->exists($existingDocument->file_path)) {
                Storage::disk('public')->delete($existingDocument->file_path);
            }

            // Update existing document
            $dto = DocumentUpdateDTO::fromArray([
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'verification_status' => 'pending',
            ]);

            return $this->update($existingDocument, $dto);
        } else {
            // Create new document
            $dto = DocumentDTO::fromArray([
                'accreditation_application_id' => $application->id,
                'document_type' => $documentType,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'verification_status' => 'pending',
                'is_required' => $this->isRequiredDocument($documentType, $application->category->name ?? ''),
            ]);

            return $this->create($dto);
        }
    }

    /**
     * Find user document by ID
     */
    public function findUserDocument(int $userId, int $documentId): ?Document
    {
        return $this->repository->findUserDocument($userId, $documentId);
    }

    /**
     * Delete document with validation
     */
    public function deleteDocument(Document $document): bool
    {
        // Check if application is in draft status
        if ($document->accreditationApplication->status !== 'draft') {
            throw new \InvalidArgumentException('Documents can only be deleted for draft applications');
        }

        return $this->delete($document);
    }

    /**
     * Get download URL for document
     */
    public function getDownloadUrl(Document $document): ?string
    {
        if (!$document->file_path || !Storage::disk('public')->exists($document->file_path)) {
            return null;
        }

        return Storage::disk('public')->url($document->file_path);
    }

    /**
     * Check if document type is required for category
     */
    protected function isRequiredDocument(string $documentType, string $categoryName): bool
    {
        $requiredDocuments = [
            'lessor' => ['cac_certificate', 'tax_clearance', 'bank_statement', 'financial_statement'],
            'lessee' => ['cac_certificate', 'tax_clearance', 'bank_statement'],
            'practitioners' => ['professional_certificate', 'cv_resume', 'reference_letter'],
        ];

        $category = strtolower($categoryName);

        return in_array($documentType, $requiredDocuments[$category] ?? []);
    }
}
