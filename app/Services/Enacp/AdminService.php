<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\Models\Enacp\AccreditationApplication;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AdminService
{
    /**
     * Get dashboard statistics
     */
    public function getDashboardStatistics(): array
    {
        return [
            'totalUsers' => User::where('role', 'user')->count(),
            'totalApplications' => AccreditationApplication::count(),
            'totalRevenue' => AccreditationApplication::where('fee_paid', 1)->sum('fee_amount'),
            'pendingApplications' => AccreditationApplication::where('status', 'submitted')->count(),
            'approvedApplications' => AccreditationApplication::where('status', 'approved')->count(),
            'rejectedApplications' => AccreditationApplication::where('status', 'rejected')->count(),
        ];
    }

    /**
     * Get recent applications for admin dashboard
     */
    public function getRecentApplications(int $limit = 10): Collection
    {
        return AccreditationApplication::with(['user', 'category', 'subtype'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get reports and analytics
     */
    public function getReportsAndAnalytics(): array
    {
        return [
            'monthly_applications' => $this->getMonthlyApplications(),
            'category_distribution' => $this->getCategoryDistribution(),
            'revenue_analytics' => $this->getRevenueAnalytics(),
            'approval_rate' => $this->getApprovalRate(),
        ];
    }

    /**
     * Get monthly applications data
     */
    protected function getMonthlyApplications(): array
    {
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = [
                'month' => $date->format('M Y'),
                'applications' => AccreditationApplication::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
            ];
        }

        return $months;
    }

    /**
     * Get category distribution
     */
    protected function getCategoryDistribution(): array
    {
        $total = AccreditationApplication::count();
        $categories = AccreditationApplication::with('category')
            ->select('stakeholder_category_id', DB::raw('count(*) as count'))
            ->groupBy('stakeholder_category_id')
            ->get()
            ->map(function ($item) use ($total) {
                $percentage = $total > 0 ? round(($item->count / $total) * 100, 1) : 0;

                return [
                    'name' => $item->category->name ?? 'Unknown',
                    'count' => $item->count,
                    'percentage' => $percentage,
                ];
            })
            ->toArray();

        return $categories;
    }

    /**
     * Get revenue analytics
     */
    protected function getRevenueAnalytics(): array
    {
        $totalRevenue = AccreditationApplication::where('fee_paid', 1)->sum('fee_amount');
        $totalApplications = AccreditationApplication::where('fee_paid', 1)->count();
        $avgRevenuePerApp = $totalApplications > 0 ? $totalRevenue / $totalApplications : 0;

        // Calculate revenue growth (comparing current month to previous month)
        $currentMonthRevenue = AccreditationApplication::where('fee_paid', 1)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('fee_amount');

        $previousMonthRevenue = AccreditationApplication::where('fee_paid', 1)
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('fee_amount');

        $revenueGrowth = $previousMonthRevenue > 0 ?
            (($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100 : 0;

        // Calculate average processing time (days between submission and approval/rejection)
        $avgProcessingDays = AccreditationApplication::whereIn('status', ['approved', 'rejected'])
            ->whereNotNull('status_updated_at')
            ->get()
            ->avg(function ($app) {
                return $app->created_at->diffInDays($app->status_updated_at);
            }) ?? 0;

        return [
            'total_revenue' => $totalRevenue,
            'avg_revenue_per_application' => $avgRevenuePerApp,
            'revenue_growth' => round($revenueGrowth, 1),
            'avg_processing_days' => round($avgProcessingDays, 1),
        ];
    }

    /**
     * Get approval rate
     */
    protected function getApprovalRate(): array
    {
        $total = AccreditationApplication::count();
        $approved = AccreditationApplication::where('status', 'approved')->count();
        $rejected = AccreditationApplication::where('status', 'rejected')->count();
        $pending = AccreditationApplication::where('status', 'submitted')->count();

        return [
            'total' => $total,
            'approved_count' => $approved,
            'rejected_count' => $rejected,
            'pending_count' => $pending,
            'rate' => $total > 0 ? round(($approved / $total) * 100, 1) : 0,
            'rejection_rate' => $total > 0 ? round(($rejected / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Get admin settings
     */
    public function getSettings(): array
    {
        // For now, return default settings
        // In a real application, these would be stored in a settings table or config files
        return [
            'system' => [
                'site_name' => 'ELRA Accreditation System',
                'site_description' => 'Equipment Leasing Registration and Accreditation System',
                'maintenance_mode' => false,
                'registration_enabled' => true,
                'max_file_size' => 2048,
                'allowed_file_types' => 'pdf,jpg,jpeg,png',
            ],
            'email' => [
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => 587,
                'smtp_username' => '',
                'smtp_password' => '',
                'from_email' => '<EMAIL>',
                'from_name' => 'ELRA System',
            ],
            'notifications' => [
                'email_notifications' => true,
                'application_submitted' => true,
                'application_approved' => true,
                'application_rejected' => true,
                'certificate_generated' => true,
                'renewal_reminder' => true,
            ],
            'fees' => [
                'default_currency' => 'NGN',
                'payment_gateway' => 'paystack',
                'auto_approve_payments' => false,
                'refund_enabled' => true,
            ],
            'security' => [
                'session_timeout' => 30,
                'max_login_attempts' => 5,
                'password_min_length' => 8,
                'require_strong_password' => true,
                'two_factor_auth' => false,
            ],
        ];
    }

    /**
     * Update admin settings
     */
    public function updateSettings(array $settings): void
    {
        // In a real application, you would save these settings to a database or config files
        // For now, we'll just log the update
        \Log::info('Settings updated by admin: ' . json_encode($settings));
    }
}
