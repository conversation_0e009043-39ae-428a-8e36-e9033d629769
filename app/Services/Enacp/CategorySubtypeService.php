<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\CategorySubtypeDTO;
use App\DTOs\Enacp\CategorySubtypeUpdateDTO;
use App\Models\Enacp\CategorySubtype;
use App\Models\Enacp\StakeholderCategory;
use App\Repositories\Enacp\CategorySubtypeRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategorySubtypeService
{
    public function __construct(
        protected CategorySubtypeRepository $repository
    ) {
    }

    /**
     * Create a new category subtype
     */
    public function create(CategorySubtypeDTO $dto): CategorySubtype
    {
        // Ensure unique slug
        $data = $dto->toArray();
        if ($this->repository->slugExists($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['slug']);
        }

        // Set sort order if not provided
        if ($data['sort_order'] === 0) {
            $data['sort_order'] = $this->repository->getNextSortOrderForCategory($data['stakeholder_category_id']);
        }

        return $this->repository->create($data);
    }

    /**
     * Update an existing category subtype
     */
    public function update(CategorySubtype $subtype, CategorySubtypeUpdateDTO $dto): CategorySubtype
    {
        $data = $dto->toArray();

        // Ensure unique slug if being updated
        if (isset($data['slug']) && $this->repository->slugExists($data['slug'], $subtype->id)) {
            $data['slug'] = $this->generateUniqueSlug($data['slug'], $subtype->id);
        }

        return $this->repository->update($subtype, $data);
    }

    /**
     * Delete a category subtype
     */
    public function delete(CategorySubtype $subtype): bool
    {
        // Check if subtype has applications
        if ($subtype->applications()->exists()) {
            throw new \InvalidArgumentException('Cannot delete subtype with existing applications.');
        }

        // Check if subtype has applications
        if ($this->repository->hasApplications($subtype->id)) {
            return false; // Cannot delete subtype with existing applications
        }

        return $this->repository->delete($subtype);
    }

    /**
     * Find a category subtype by ID
     */
    public function findById(int $id): ?CategorySubtype
    {
        return $this->repository->findById($id);
    }

    /**
     * Find a category subtype by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = ['category']): ?CategorySubtype
    {
        return $this->repository->findByIdWithRelations($id, $relations);
    }

    /**
     * Find a category subtype by slug
     */
    public function findBySlug(string $slug): ?CategorySubtype
    {
        return $this->repository->findBySlug($slug);
    }

    /**
     * Get all category subtypes
     */
    public function getAll(): Collection
    {
        return $this->repository->getAll();
    }

    /**
     * Get all category subtypes with relationships
     */
    public function getAllWithRelations(array $relations = ['category']): Collection
    {
        return $this->repository->getAllWithRelations($relations);
    }

    /**
     * Get paginated category subtypes
     */
    public function getPaginated(int $perPage = 15, array $relations = ['category']): LengthAwarePaginator
    {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Get active category subtypes
     */
    public function getActive(): Collection
    {
        return $this->repository->getActive();
    }

    /**
     * Get active category subtypes with relationships
     */
    public function getActiveWithRelations(array $relations = ['category']): Collection
    {
        return $this->repository->getActiveWithRelations($relations);
    }

    /**
     * Get subtypes by stakeholder category ID
     */
    public function getByCategoryId(int $categoryId): Collection
    {
        return $this->repository->getByCategoryId($categoryId);
    }

    /**
     * Get active subtypes by stakeholder category ID
     */
    public function getActiveByCategoryId(int $categoryId): Collection
    {
        return $this->repository->getActiveByCategoryId($categoryId);
    }

    /**
     * Get subtypes ordered by sort order
     */
    public function getOrderedBySort(): Collection
    {
        return $this->repository->getOrderedBySort();
    }

    /**
     * Get active subtypes ordered by sort order
     */
    public function getActiveOrderedBySort(): Collection
    {
        return $this->repository->getActiveOrderedBySort();
    }

    /**
     * Search subtypes by name
     */
    public function searchByName(string $name): Collection
    {
        return $this->repository->searchByName($name);
    }

    /**
     * Get subtypes by currency
     */
    public function getByCurrency(string $currency): Collection
    {
        return $this->repository->getByCurrency($currency);
    }

    /**
     * Get subtypes with fee range
     */
    public function getByFeeRange(float $minFee, float $maxFee, string $feeType = 'accreditation_fee'): Collection
    {
        return $this->repository->getByFeeRange($minFee, $maxFee, $feeType);
    }

    /**
     * Toggle subtype active status
     */
    public function toggleActive(CategorySubtype $subtype): CategorySubtype
    {
        $dto = CategorySubtypeUpdateDTO::fromArray(['is_active' => !$subtype->is_active]);
        return $this->update($subtype, $dto);
    }

    /**
     * Reorder subtypes within a category
     */
    public function reorderInCategory(int $categoryId, array $subtypeIds): bool
    {
        return $this->repository->reorderInCategory($categoryId, $subtypeIds);
    }

    /**
     * Get subtypes with application counts
     */
    public function getWithApplicationCounts(): Collection
    {
        return $this->repository->getWithApplicationCounts();
    }

    /**
     * Update fees for a subtype
     */
    public function updateFees(CategorySubtype $subtype, float $accreditationFee, float $renewalFee): CategorySubtype
    {
        $dto = CategorySubtypeUpdateDTO::fromArray([
            'accreditation_fee' => $accreditationFee,
            'renewal_fee' => $renewalFee,
        ]);

        return $this->update($subtype, $dto);
    }

    /**
     * Get subtype statistics
     */
    public function getStatistics(): array
    {
        $subtypes = $this->repository->getWithApplicationCounts();

        return [
            'total_subtypes' => $subtypes->count(),
            'active_subtypes' => $subtypes->where('is_active', true)->count(),
            'total_applications' => $subtypes->sum('applications_count'),
            'average_accreditation_fee' => $subtypes->avg('accreditation_fee'),
            'average_renewal_fee' => $subtypes->avg('renewal_fee'),
            'currencies_used' => $subtypes->pluck('currency')->unique()->values(),
        ];
    }

    /**
     * Create a subtype for a specific category
     */
    public function createForCategory(StakeholderCategory $category, CategorySubtypeDTO $dto): CategorySubtype
    {
        $data = $dto->toArray();
        $data['stakeholder_category_id'] = $category->id;

        // Set sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = $this->repository->getNextSortOrderForCategory($category->id);
        }

        return $this->repository->create($data);
    }

    /**
     * Find subtype by ID and category
     */
    public function findByIdAndCategory(int $id, int $categoryId): ?CategorySubtype
    {
        return $this->repository->findByIdAndCategory($id, $categoryId);
    }

    /**
     * Generate a unique slug
     */
    protected function generateUniqueSlug(string $baseSlug, ?int $excludeId = null): string
    {
        $slug = $baseSlug;
        $counter = 1;

        while ($this->repository->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
