<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\Classes\QrCodeGenerator;
use App\DTOs\Enacp\CertificateDTO;
use App\DTOs\Enacp\CertificateUpdateDTO;
use App\Models\Enacp\AccreditationApplication;
use App\Models\Enacp\Certificate;
use App\Repositories\Enacp\CertificateRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;

class CertificateService
{
    public function __construct(
        protected CertificateRepository $repository
    ) {
    }

    /**
     * Create a new certificate
     */
    public function create(CertificateDTO $dto): Certificate
    {
        $data = $dto->toArray();

        // Generate certificate number if not provided
        if (empty($data['certificate_number'])) {
            $data['certificate_number'] = $this->generateCertificateNumber();
        }

        return $this->repository->create($data);
    }

    /**
     * Update an existing certificate
     */
    public function update(Certificate $certificate, CertificateUpdateDTO $dto): Certificate
    {
        return $this->repository->update($certificate, $dto->toArray());
    }

    /**
     * Delete a certificate
     */
    public function delete(Certificate $certificate): bool
    {
        return $this->repository->delete($certificate);
    }

    /**
     * Find a certificate by ID
     */
    public function findById(int $id): ?Certificate
    {
        return $this->repository->findById($id);
    }

    /**
     * Find a certificate by certificate number
     */
    public function findByCertificateNumber(string $certificateNumber): ?Certificate
    {
        return $this->repository->findByCertificateNumber($certificateNumber);
    }

    /**
     * Get paginated certificates
     */
    public function getPaginated(int $perPage = 15, array $relations = ['accreditationApplication']): LengthAwarePaginator
    {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Get certificates by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->repository->getByStatus($status);
    }

    /**
     * Get active certificates
     */
    public function getActive(): Collection
    {
        return $this->repository->getActive();
    }

    /**
     * Get certificates expiring soon
     */
    public function getExpiringSoon(int $days = 30): Collection
    {
        return $this->repository->getExpiringSoon($days);
    }

    /**
     * Get expired certificates
     */
    public function getExpired(): Collection
    {
        return $this->repository->getExpired();
    }

    /**
     * Search certificates
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->search($criteria, $perPage);
    }

    /**
     * Get certificates statistics
     */
    public function getStatistics(): array
    {
        return $this->repository->getStatistics();
    }

    /**
     * Generate a unique certificate number (moved from model)
     */
    public function generateCertificateNumber(string $category = 'lessor'): string
    {
        // Generate a random 12-digit number for better security
        do {
            $randomNumber = mt_rand(100000000000, 999999999999); // 12 digits
            $certificateNumber = (string) $randomNumber;

            // Check if this number already exists
            $exists = $this->repository->certificateNumberExists($certificateNumber);
        } while ($exists);

        return $certificateNumber;
    }

    /**
     * Check if certificate is expired (moved from model)
     */
    public function isExpired(Certificate $certificate): bool
    {
        return $certificate->expiry_date && $certificate->expiry_date->isPast();
    }

    /**
     * Check if certificate is active (moved from model)
     */
    public function isActive(Certificate $certificate): bool
    {
        return $certificate->status === 'active' && !$this->isExpired($certificate);
    }

    /**
     * Suspend a certificate
     */
    public function suspend(Certificate $certificate, ?string $reason = null): Certificate
    {
        $dto = CertificateUpdateDTO::fromArray([
            'status' => 'suspended',
            'certificate_data' => array_merge($certificate->certificate_data ?? [], [
                'suspension_reason' => $reason,
                'suspended_at' => now()->toISOString(),
            ]),
        ]);

        return $this->update($certificate, $dto);
    }

    /**
     * Revoke a certificate
     */
    public function revoke(Certificate $certificate, ?string $reason = null): Certificate
    {
        $dto = CertificateUpdateDTO::fromArray([
            'status' => 'revoked',
            'certificate_data' => array_merge($certificate->certificate_data ?? [], [
                'revocation_reason' => $reason,
                'revoked_at' => now()->toISOString(),
            ]),
        ]);

        return $this->update($certificate, $dto);
    }

    /**
     * Reactivate a suspended certificate
     */
    public function reactivate(Certificate $certificate): Certificate
    {
        if ($certificate->status !== 'suspended') {
            throw new \InvalidArgumentException('Only suspended certificates can be reactivated.');
        }

        $dto = CertificateUpdateDTO::fromArray([
            'status' => 'active',
            'certificate_data' => array_merge($certificate->certificate_data ?? [], [
                'reactivated_at' => now()->toISOString(),
            ]),
        ]);

        return $this->update($certificate, $dto);
    }

    /**
     * Generate certificate for an approved application
     */
    public function generateForApplication(AccreditationApplication $application): Certificate
    {
        // Generate certificate number based on category
        $category = strtolower($application->category->name ?? 'lessor');
        $certificateNumber = Certificate::generateCertificateNumber($category);

        // Generate verification URL - use frontend URL
        $verificationUrl = 'http://localhost:5173/verify-certificate?certificate=' . $certificateNumber;

        // Generate QR code
        $qrCodeDataUri = QrCodeGenerator::generateForCertificate($certificateNumber, $verificationUrl);

        // Generate certificate PDF with QR code
        $pdfPath = $this->generateCertificatePDF($application, $certificateNumber, $qrCodeDataUri);

        // Generate certificate image with QR code
        $imagePath = $this->generateCertificateImage($application, $certificateNumber, $qrCodeDataUri);

        // Create certificate record
        $certificate = Certificate::create([
            'accreditation_application_id' => $application->id,
            'certificate_number' => $certificateNumber,
            'qr_code_path' => null, // QR code is embedded in PDF/image
            'certificate_path' => $pdfPath,
            'certificate_image_path' => $imagePath,
            'issued_date' => now(),
            'expiry_date' => now()->addYear(),
            'status' => 'active',
            'certificate_data' => [
                'issued_by' => 'Equipment Leasing Registration Authority',
                'category' => $application->category->name ?? 'N/A',
                'subtype' => $application->subtype->name ?? 'N/A',
                'company_name' => $application->application_data['company_name'] ?? 'N/A',
                'applicant_name' => $application->application_data['first_name'] . ' ' . $application->application_data['last_name'],
                'verification_url' => $verificationUrl,
            ],
        ]);

        // Add QR code URL to the certificate data for frontend access
        $certificate->qr_code_url = $qrCodeDataUri;

        return $certificate;
    }

    /**
     * Find certificate by application ID
     */
    public function findByApplicationId(int $applicationId): ?Certificate
    {
        return $this->repository->findByApplicationId($applicationId);
    }

    /**
     * Find certificate by certificate number with relationships
     */
    public function findByCertificateNumberWithRelations(string $certificateNumber, array $relations = []): ?Certificate
    {
        return $this->repository->findByCertificateNumberWithRelations($certificateNumber, $relations);
    }

    /**
     * Get certificate download URL
     */
    public function getCertificateDownloadUrl(Certificate $certificate): ?string
    {
        if (!$certificate->certificate_path) {
            return null;
        }

        return Storage::disk('public')->url($certificate->certificate_path);
    }

    /**
     * Get certificate image download URL
     */
    public function getCertificateImageDownloadUrl(Certificate $certificate): ?string
    {
        if (!$certificate->certificate_image_path) {
            return null;
        }

        return Storage::disk('public')->url($certificate->certificate_image_path);
    }

    /**
     * Get verification data for a certificate
     */
    public function getVerificationData(Certificate $certificate): array
    {
        return [
            'is_valid' => $certificate->isActive(),
            'status' => $certificate->status,
            'expired' => $certificate->isExpired(),
            'application' => $certificate->accreditationApplication,
        ];
    }

    /**
     * Generate certificate PDF with QR code
     */
    protected function generateCertificatePDF(AccreditationApplication $application, string $certificateNumber, string $qrCodeDataUri): string
    {
        $data = [
            'certificate_number' => $certificateNumber,
            'application_number' => $application->application_number,
            'applicant_name' => $application->application_data['first_name'] . ' ' . $application->application_data['last_name'],
            'company_name' => $application->application_data['company_name'] ?? 'N/A',
            'category' => strtolower($application->category->name ?? 'lessor'),
            'subtype' => $application->subtype->name ?? 'N/A',
            'issued_date' => now()->format('jS \d\a\y \of F, Y'),
            'expiry_date' => now()->addYear()->format('31st December, Y'),
            'qr_code_data_uri' => $qrCodeDataUri,
        ];

        $pdf = PDF::loadView('certificates.enacp.template', $data);
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'Inter',
            'chroot' => public_path(),
        ]);

        $pdfPath = 'certificates/pdfs/' . $certificateNumber . '.pdf';
        Storage::disk('public')->put($pdfPath, $pdf->output());

        return $pdfPath;
    }

    /**
     * Generate certificate image with QR code
     */
    protected function generateCertificateImage(AccreditationApplication $application, string $certificateNumber, string $qrCodeDataUri): string
    {
        $data = [
            'certificate_number' => $certificateNumber,
            'application_number' => $application->application_number,
            'applicant_name' => $application->application_data['first_name'] . ' ' . $application->application_data['last_name'],
            'company_name' => $application->application_data['company_name'] ?? 'N/A',
            'category' => strtolower($application->category->name ?? 'lessor'),
            'subtype' => $application->subtype->name ?? 'N/A',
            'issued_date' => now()->format('jS \d\a\y \of F, Y'),
            'expiry_date' => now()->addYear()->format('31st December, Y'),
            'qr_code_data_uri' => $qrCodeDataUri,
        ];

        // For now, we'll use the same template but save as image
        // In a production environment, you might want to use a headless browser like Puppeteer
        // to convert HTML to image
        $html = view('certificates.enacp.template', $data)->render();

        $imagePath = 'certificates/images/' . $certificateNumber . '.html';
        Storage::disk('public')->put($imagePath, $html);

        return $imagePath;
    }
}
