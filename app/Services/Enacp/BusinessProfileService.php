<?php

declare(strict_types=1);

namespace App\Services\Enacp;

use App\DTOs\Enacp\BusinessProfileDTO;
use App\Models\Enacp\BusinessProfile;
use App\Models\User;
use App\Repositories\Enacp\BusinessProfileRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BusinessProfileService
{
    public function __construct(
        protected BusinessProfileRepository $repository
    ) {
    }

    /**
     * Create a new business profile
     */
    public function create(array $data): BusinessProfile
    {
        return $this->repository->create($data);
    }

    /**
     * Create a business profile for a user during registration
     */
    public function createForUser(User $user, BusinessProfileDTO $dto): BusinessProfile
    {
        $data = $dto->toArray();
        $data['created_by'] = $user->id;
        $data['modified_by'] = $user->id;

        return $this->repository->create($data);
    }

    /**
     * Update an existing business profile
     */
    public function update(BusinessProfile $businessProfile, array $data): BusinessProfile
    {
        // Handle profile picture upload if present
        if (isset($data['profile_picture']) && $data['profile_picture'] instanceof UploadedFile) {
            // Delete old profile picture if exists
            if ($businessProfile->profile_picture) {
                $this->deleteProfilePicture($businessProfile->profile_picture);
            }

            $data['profile_picture'] = $this->handleProfilePictureUpload($data['profile_picture']);
        }

        return $this->repository->update($businessProfile, $data);
    }

    /**
     * Delete a business profile
     */
    public function delete(BusinessProfile $businessProfile): bool
    {
        return $this->repository->delete($businessProfile);
    }

    /**
     * Find a business profile by ID
     */
    public function findById(int $id): ?BusinessProfile
    {
        return $this->repository->findById($id);
    }

    /**
     * Find a business profile by ID with relationships
     */
    public function findByIdWithRelations(int $id, array $relations = ['categorySubtype']): ?BusinessProfile
    {
        return $this->repository->findByIdWithRelations($id, $relations);
    }

    /**
     * Get all business profiles
     */
    public function getAll(): Collection
    {
        return $this->repository->getAll();
    }

    /**
     * Get all business profiles with relationships
     */
    public function getAllWithRelations(array $relations = ['categorySubtype']): Collection
    {
        return $this->repository->getAllWithRelations($relations);
    }

    /**
     * Get paginated business profiles
     */
    public function getPaginated(int $perPage = 15, array $relations = ['categorySubtype']): LengthAwarePaginator
    {
        return $this->repository->getPaginated($perPage, $relations);
    }

    /**
     * Search business profiles by various criteria
     */
    public function search(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->search($criteria, $perPage);
    }

    /**
     * Find business profiles by company name
     */
    public function findByCompanyName(string $companyName): Collection
    {
        return $this->repository->findByCompanyName($companyName);
    }

    /**
     * Find business profiles by business type
     */
    public function findByBusinessType(string $businessType): Collection
    {
        return $this->repository->findByBusinessType($businessType);
    }

    /**
     * Find business profiles by country
     */
    public function findByCountry(string $country): Collection
    {
        return $this->repository->findByCountry($country);
    }

    /**
     * Find business profiles by category subtype
     */
    public function findByCategorySubtype(int $categorySubtypeId): Collection
    {
        return $this->repository->findByCategorySubtype($categorySubtypeId);
    }

    /**
     * Find business profile by payment reference
     */
    public function findByPaymentReference(string $paymentReference): ?BusinessProfile
    {
        return $this->repository->findByPaymentReference($paymentReference);
    }

    /**
     * Get active business profiles
     */
    public function getActive(): Collection
    {
        return $this->repository->getActive();
    }

    /**
     * Get business profiles with minimum revenue
     */
    public function getByMinimumRevenue(float $minimumRevenue): Collection
    {
        return $this->repository->getByMinimumRevenue($minimumRevenue);
    }

    /**
     * Get business profiles by employee range
     */
    public function getByEmployeeRange(int $minEmployees, int $maxEmployees): Collection
    {
        return $this->repository->getByEmployeeRange($minEmployees, $maxEmployees);
    }

    /**
     * Get business statistics
     */
    public function getStatistics(): array
    {
        $allProfiles = $this->repository->getAll();

        return [
            'total_profiles' => $allProfiles->count(),
            'active_profiles' => $this->repository->getActive()->count(),
            'average_revenue' => $allProfiles->avg('annual_revenue'),
            'total_employees' => $allProfiles->sum('number_of_employees'),
            'business_types' => $allProfiles->groupBy('business_type')->map->count(),
            'countries' => $allProfiles->groupBy('country')->map->count(),
        ];
    }

    /**
     * Handle profile picture upload
     */
    protected function handleProfilePictureUpload(UploadedFile $file): string
    {
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs('business-profiles', $filename, 'public');
    }

    /**
     * Delete profile picture
     */
    protected function deleteProfilePicture(string $path): void
    {
        if (Storage::disk('public')->exists($path)) {
            Storage::disk('public')->delete($path);
        }
    }

    /**
     * Validate payment reference uniqueness
     */
    public function isPaymentReferenceUnique(string $paymentReference, ?int $excludeId = null): bool
    {
        $profile = $this->repository->findByPaymentReference($paymentReference);

        if (!$profile) {
            return true;
        }

        return $excludeId && $profile->id === $excludeId;
    }
}
