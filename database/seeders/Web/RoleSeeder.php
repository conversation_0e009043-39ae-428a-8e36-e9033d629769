<?php

declare(strict_types=1);

namespace Database\Seeders\Web;

use App\Models\User;
use App\Models\Web\AccessControl\Role;
use App\Services\Shared\UserService;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function __construct(protected UserService $userService)
    {
        //
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            ['Super Administrator', 'super_admin', 'web', 'This is the role of the super administrator'],
            ['Administrator', 'admin', 'web', 'This is the role of the regular administrator'],
            ['Developer', 'dev', 'web', 'This is the role of the developer'],
            ['Guest', 'user', 'web', 'This is the role of the guest'],
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(
                ['name' => $role[0]],
                [
                    'slug' => $role[1],
                    'guard_name' => $role[2],
                    'description' => $role[3],
                    'is_active' => true,
                    'created_by' => 1,
                    'modified_by' => 1,
                ]
            );
        }

        $users = User::where('id', '<', 4)->get();
        foreach ($users as $user) {
            if ($user->identifier === 'super-admin') {
                $this->userService->syncRoles($user, [1]);
            }
            if ($user->identifier === 'regular-admin') {
                $this->userService->syncRoles($user, [2]);
            }
            if ($user->identifier === 'developer') {
                $this->userService->syncRoles($user, [3]);
            }
        }

        $this->command->info('Roles assigned successfully!');
        $this->command->info('Super Admin role <NAME_EMAIL>');
        $this->command->info('Admin role has been <NAME_EMAIL>');
    }
}
