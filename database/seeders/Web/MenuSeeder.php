<?php

declare(strict_types=1);

namespace Database\Seeders\Web;

use App\Models\Web\Shared\Menu;
use App\Models\Web\Shared\SubModule;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $menusBySubModules = [
            'users-lists' => ['ki-outline ki-people', 'users.index', 0, 'users'],
            'access-control' => [
                'ki-outline ki-abstract-28',
                null,
                1,
                'access-control',
                [
                    'roles-lists' => [null, 'roles.index', 1, 'access-control'],
                    'permissions' => [null, 'permissions.index', 2, 'access-control'],
                ],
            ],
        ];

        foreach ($menusBySubModules as $name => $details) {
            $this->createMenu($name, $details);
        }
    }

    public function createMenu(string $name, array $details, ?int $parentId = null): void
    {
        $subModule = SubModule::where('code', $details[3])->first();
        $menu = Menu::updateOrCreate(
            ['title' => ucwords(string: str_replace(['-', '_'], [' ', ' & '], $name))],
            [
                'icon' => $details[0],
                'route' => $details[1],
                'order' => $details[2],
                'created_by' => 1,
                'modified_by' => 1,
                'sub_module_id' => $subModule->id,
                'parent_id' => $parentId,
            ]
        );

        if (isset($details[4]) && is_array($details[4])) {
            foreach ($details[4] as $name => $detail) {
                $this->createMenu($name, $detail, $menu->id);
            }
        }
    }
}
