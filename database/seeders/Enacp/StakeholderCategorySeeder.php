<?php

declare(strict_types=1);

namespace Database\Seeders\Enacp;

use App\Models\Enacp\CategorySubtype;
use App\Models\Enacp\StakeholderCategory;
use Illuminate\Database\Seeder;

class StakeholderCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Lessor',
                'slug' => 'lessor',
                'description' => 'Companies or individuals who lease out equipment. Includes financiers, fleet owners, and asset owners.',
                'icon' => 'Building2',
                'sort_order' => 1,
                'subtypes' => [
                    [
                        'name' => 'Less than 100M shares capital',
                        'slug' => 'less-than-100m-shares',
                        'accreditation_fee' => 100000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'More than 100M shares capital',
                        'slug' => 'more-than-100m-shares',
                        'accreditation_fee' => 250000,
                        'renewal_fee' => 250000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                    [
                        'name' => 'International Firm',
                        'slug' => 'international-firm',
                        'accreditation_fee' => 1000,
                        'renewal_fee' => 500,
                        'currency' => 'USD',
                        'sort_order' => 3,
                    ],
                ],
            ],
            [
                'name' => 'Lessee',
                'slug' => 'lessee',
                'description' => 'Verified organizations or individuals leasing equipment. May include MDAs, contractors, or SMEs.',
                'icon' => 'FileText',
                'sort_order' => 2,
                'subtypes' => [
                    [
                        'name' => 'Corporate',
                        'slug' => 'corporate',
                        'accreditation_fee' => 15000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Individual',
                        'slug' => 'individual',
                        'accreditation_fee' => 10000,
                        'renewal_fee' => 5000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Practitioners',
                'slug' => 'practitioners',
                'description' => 'Professionals including lease managers, analysts, compliance officers, and asset managers.',
                'icon' => 'UserCheck',
                'sort_order' => 3,
                'subtypes' => [
                    [
                        'name' => 'Lease Managers',
                        'slug' => 'lease-managers',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Analysts',
                        'slug' => 'analysts',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                    [
                        'name' => 'Compliance Officers',
                        'slug' => 'compliance-officers',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 3,
                    ],
                    [
                        'name' => 'Asset Managers',
                        'slug' => 'asset-managers',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 4,
                    ],
                ],
            ],
            [
                'name' => 'Brokers',
                'slug' => 'brokers',
                'description' => 'Licensed intermediaries facilitating lease transactions.',
                'icon' => 'Handshake',
                'sort_order' => 4,
                'subtypes' => [
                    [
                        'name' => 'Brokers',
                        'slug' => 'brokers',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Accredited Affiliate Agents',
                        'slug' => 'accredited-affiliate-agents',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Auxiliary Service Providers',
                'slug' => 'auxiliary',
                'description' => 'Insurance, maintenance, logistics, recovery, and valuation firms.',
                'icon' => 'Wrench',
                'sort_order' => 5,
                'subtypes' => [
                    [
                        'name' => 'Insurance',
                        'slug' => 'insurance',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Maintenance',
                        'slug' => 'maintenance',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                    [
                        'name' => 'Logistics',
                        'slug' => 'logistics',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 3,
                    ],
                    [
                        'name' => 'Recovery',
                        'slug' => 'recovery',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 4,
                    ],
                    [
                        'name' => 'Valuation',
                        'slug' => 'valuation',
                        'accreditation_fee' => 40000,
                        'renewal_fee' => 10000,
                        'currency' => 'NGN',
                        'sort_order' => 5,
                    ],
                ],
            ],
            [
                'name' => 'Training Institutions',
                'slug' => 'training',
                'description' => 'Organizations offering certified training in leasing and asset management.',
                'icon' => 'GraduationCap',
                'sort_order' => 6,
                'subtypes' => [
                    [
                        'name' => 'University/Polytechnic',
                        'slug' => 'university-polytechnic',
                        'accreditation_fee' => 250000,
                        'renewal_fee' => 100000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Professional Training Institute',
                        'slug' => 'professional-training-institute',
                        'accreditation_fee' => 100000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Equipment Manufacturers & OEMs',
                'slug' => 'manufacturers',
                'description' => 'Local and international original equipment manufacturers.',
                'icon' => 'Factory',
                'sort_order' => 7,
                'subtypes' => [
                    [
                        'name' => 'Local',
                        'slug' => 'local',
                        'accreditation_fee' => 250000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Public',
                        'slug' => 'public',
                        'accreditation_fee' => 1000000,
                        'renewal_fee' => 500000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                    [
                        'name' => 'International',
                        'slug' => 'international',
                        'accreditation_fee' => 1000,
                        'renewal_fee' => 500,
                        'currency' => 'USD',
                        'sort_order' => 3,
                    ],
                ],
            ],
            [
                'name' => 'Accredited Equipment',
                'slug' => 'equipment',
                'description' => 'Physical equipment meeting operational, safety, and documentation standards.',
                'icon' => 'Truck',
                'sort_order' => 8,
                'subtypes' => [
                    [
                        'name' => 'Local',
                        'slug' => 'local-equipment',
                        'accreditation_fee' => 75000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Abroad',
                        'slug' => 'abroad-equipment',
                        'accreditation_fee' => 500,
                        'renewal_fee' => 200,
                        'currency' => 'USD',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Lease-Focused Trade Associations',
                'slug' => 'associations',
                'description' => 'Industry and professional bodies promoting standards and advocacy.',
                'icon' => 'Building',
                'sort_order' => 9,
                'subtypes' => [
                    [
                        'name' => 'Trade Associations',
                        'slug' => 'trade-associations',
                        'accreditation_fee' => 1000000,
                        'renewal_fee' => 500000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Cooperative Society',
                        'slug' => 'cooperative-society',
                        'accreditation_fee' => 250000,
                        'renewal_fee' => 100000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Student Fellowship / Young Professionals',
                'slug' => 'students',
                'description' => 'Emerging professionals undergoing industry-focused training or mentorship.',
                'icon' => 'Target',
                'sort_order' => 10,
                'subtypes' => [
                    [
                        'name' => 'Students',
                        'slug' => 'students',
                        'accreditation_fee' => 10000,
                        'renewal_fee' => 5000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Young Professionals',
                        'slug' => 'young-professionals',
                        'accreditation_fee' => 10000,
                        'renewal_fee' => 5000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
            [
                'name' => 'Compliance Advisory Firms',
                'slug' => 'advisory',
                'description' => 'Third-party assessors or firms providing regulatory and operational audits.',
                'icon' => 'BarChart3',
                'sort_order' => 11,
                'subtypes' => [
                    [
                        'name' => 'Compliance Advisory',
                        'slug' => 'compliance-advisory',
                        'accreditation_fee' => 100000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 1,
                    ],
                    [
                        'name' => 'Regulatory Audits',
                        'slug' => 'regulatory-audits',
                        'accreditation_fee' => 100000,
                        'renewal_fee' => 50000,
                        'currency' => 'NGN',
                        'sort_order' => 2,
                    ],
                ],
            ],
        ];

        foreach ($categories as $categoryData) {
            $subtypes = $categoryData['subtypes'];
            unset($categoryData['subtypes']);

            $category = StakeholderCategory::create($categoryData);

            foreach ($subtypes as $subtypeData) {
                $subtypeData['stakeholder_category_id'] = $category->id;
                CategorySubtype::create($subtypeData);
            }
        }
    }
}
