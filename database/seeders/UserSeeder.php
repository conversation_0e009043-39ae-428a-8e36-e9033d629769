<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $default_password = 'admin123';

        $accounts = [
            [
                'identifier' => 'super-admin',
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt($default_password),
                'email_verified_at' => now(),
                'status' => 'active',
            ],
            [
                'identifier' => 'regular-admin',
                'first_name' => 'Regular',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt($default_password),
                'email_verified_at' => now(),
                'status' => 'active',
            ],
            [
                'identifier' => 'developer',
                'first_name' => 'Developer',
                'last_name' => 'ELRA',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'status' => 'active',
            ],
        ];

        foreach ($accounts as $account) {
            User::updateOrCreate(
                ['identifier' => $account['identifier']],
                [
                    'first_name' => $account['first_name'],
                    'last_name' => $account['last_name'],
                    'email' => $account['email'],
                    'phone' => null,
                    'password' => $account['password'],
                    'profile_picture' => null,
                    'status' => 'active',
                    'email_verified_at' => $account['email_verified_at'],
                    'last_login_ip' => null,
                    'first_access' => null,
                    'last_login_at' => null,
                    'remember_token' => null,
                ]
            );
        }

        $this->command->info('Admin users created successfully!');
        $this->command->info('Super Admin: <EMAIL> / admin123');
        $this->command->info('Regular Admin: <EMAIL> / admin123');
    }
}
