<?php

declare(strict_types=1);

namespace Database\Seeders;

use Database\Seeders\Enacp\StakeholderCategorySeeder;
use Database\Seeders\Web\CoreSeeder;
use Database\Seeders\Web\MenuSeeder;
use Database\Seeders\Web\ModulesSubModulesPermissionsSeeder;
use Database\Seeders\Web\RoleSeeder;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CoreSeeder::class,
            UserSeeder::class,
            RoleSeeder::class,
            ModulesSubModulesPermissionsSeeder::class,
            MenuSeeder::class,
            // Enacp
            StakeholderCategorySeeder::class,
        ]);
    }
}
