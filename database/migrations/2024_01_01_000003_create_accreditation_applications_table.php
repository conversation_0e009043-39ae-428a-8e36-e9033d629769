<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accreditation_applications', function (Blueprint $table) {
            $table->id();
            $table->string('application_number')->unique()->nullable(); // Auto-generated EIN-like number
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('stakeholder_category_id')->constrained();
            $table->foreignId('category_subtype_id')->constrained();
            $table->enum('status', [
                'draft',
                'submitted',
                'under_review',
                'approved',
                'rejected',
                'suspended',
                'expired'
            ])->default('draft');
            $table->timestamp('status_updated_at')->nullable();
            $table->date('submitted_at')->nullable();
            $table->date('approved_at')->nullable();
            $table->date('expires_at')->nullable();
            $table->decimal('fee_paid', 15, 2)->default(0);
            $table->string('payment_reference')->nullable();
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->decimal('fee_amount', 15, 2)->nullable();
            $table->json('application_data'); // Store all form data as JSON
            $table->text('admin_notes')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accreditation_applications');
    }
};
