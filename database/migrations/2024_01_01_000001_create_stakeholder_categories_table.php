<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stakeholder_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., 'Lessor', 'Lessee', 'Practitioners'
            $table->string('slug')->unique(); // e.g., 'lessor', 'lessee', 'practitioners'
            $table->text('description');
            $table->string('icon'); // Lucide icon name
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stakeholder_categories');
    }
};
