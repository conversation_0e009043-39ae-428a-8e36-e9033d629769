<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('rc_number', 50)->nullable();
            $table->string('tax_id', 50)->nullable();
            $table->string('address', 500);
            $table->string('city', 100);
            $table->string('state', 100);
            $table->string('country', 100);
            $table->string('postal_code', 20)->nullable();
            $table->string('website')->nullable();
            $table->string('position', 100);
            $table->string('business_type', 100);
            $table->integer('years_in_business')->unsigned();
            $table->decimal('annual_revenue', 15, 2)->unsigned();
            $table->integer('number_of_employees')->unsigned();
            $table->text('business_description');
            $table->foreignId('category_subtype_id')->constrained('category_subtypes')->restrictOnDelete();
            $table->string('payment_reference');
            $table->foreignId('created_by')->references('id')->on('users')->restrictOnDelete();
            $table->foreignId('modified_by')->references('id')->on('users')->restrictOnDelete();
            $table->timestamps();

            // Add indexes for better query performance
            $table->index(['company_name']);
            $table->index(['business_type']);
            $table->index(['country']);
            $table->index(['category_subtype_id']);
            $table->index(['payment_reference']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_profiles');
    }
};
