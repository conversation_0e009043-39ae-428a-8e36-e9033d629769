<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('category_subtypes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stakeholder_category_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., 'Less than 100M shares capital'
            $table->string('slug')->unique(); // e.g., 'less-than-100m-shares'
            $table->decimal('accreditation_fee', 15, 2); // New application fee
            $table->decimal('renewal_fee', 15, 2); // Annual renewal fee
            $table->string('currency', 3)->default('NGN'); // NGN, USD, etc.
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('category_subtypes');
    }
};
