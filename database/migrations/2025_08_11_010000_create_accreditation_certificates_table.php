<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accreditation_certificates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('accreditation_application_id');
            $table->string('certificate_number')->unique(); // ELRA unique number
            $table->string('qr_code_path')->nullable(); // Path to stored QR code image
            $table->string('certificate_path')->nullable(); // Path to stored certificate PDF
            $table->string('certificate_image_path')->nullable();
            $table->date('issued_date');
            $table->date('expiry_date');
            $table->string('status')->default('active'); // active, expired, revoked
            $table->json('certificate_data')->nullable(); // Additional certificate data
            $table->timestamps();

            $table->foreign('accreditation_application_id')
                  ->references('id')
                  ->on('accreditation_applications')
                  ->onDelete('cascade');
            $table->index(['certificate_number', 'status']);
            $table->index('expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accreditation_certificates');
    }
};
