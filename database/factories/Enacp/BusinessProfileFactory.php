<?php

namespace Database\Factories\Enacp;

use App\Models\Enacp\BusinessProfile;
use App\Models\Enacp\CategorySubtype;
use Illuminate\Database\Eloquent\Factories\Factory;

class BusinessProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BusinessProfile::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $businessTypes = [
            'Manufacturing',
            'Technology',
            'Healthcare',
            'Finance',
            'Retail',
            'Construction',
            'Transportation',
            'Agriculture',
            'Education',
            'Hospitality'
        ];

        $positions = [
            'Chief Executive Officer',
            'Managing Director',
            'General Manager',
            'Operations Manager',
            'Business Development Manager',
            'Finance Director',
            'Chief Technology Officer',
            'Head of Operations'
        ];

        $nigerianStates = [
            'Lagos',
            'Abuja',
            'Kano',
            'Rivers',
            'Oyo',
            'Delta',
            'Kaduna',
            'Anambra',
            'Enugu',
            'Plateau'
        ];

        return [
            'company_name' => fake()->company(),
            'rc_number' => fake()->optional(0.7)->regexify('RC[0-9]{6}'),
            'tax_id' => fake()->optional(0.6)->regexify('[0-9]{8}-[0-9]{4}'),
            'address' => fake()->streetAddress(),
            'city' => fake()->city(),
            'state' => fake()->randomElement($nigerianStates),
            'country' => 'Nigeria',
            'postal_code' => fake()->optional(0.5)->postcode(),
            'website' => fake()->optional(0.6)->url(),
            'position' => fake()->randomElement($positions),
            'business_type' => fake()->randomElement($businessTypes),
            'years_in_business' => fake()->numberBetween(1, 50),
            'annual_revenue' => fake()->randomFloat(2, 100000, 50000000),
            'number_of_employees' => fake()->numberBetween(1, 1000),
            'business_description' => fake()->paragraph(3),
            'category_subtype_id' => CategorySubtype::factory(),
            'profile_picture' => null,
            'payment_reference' => fake()->unique()->regexify('PAY[0-9]{10}'),
        ];
    }

    /**
     * Indicate that the business profile has a profile picture.
     */
    public function withProfilePicture(): static
    {
        return $this->state(fn (array $attributes) => [
            'profile_picture' => 'profile-pictures/' . fake()->uuid() . '.jpg',
        ]);
    }

    /**
     * Indicate that the business profile is for a large company.
     */
    public function largeCompany(): static
    {
        return $this->state(fn (array $attributes) => [
            'annual_revenue' => fake()->randomFloat(2, 10000000, *********),
            'number_of_employees' => fake()->numberBetween(100, 5000),
            'years_in_business' => fake()->numberBetween(10, 50),
        ]);
    }

    /**
     * Indicate that the business profile is for a startup.
     */
    public function startup(): static
    {
        return $this->state(fn (array $attributes) => [
            'annual_revenue' => fake()->randomFloat(2, 50000, 1000000),
            'number_of_employees' => fake()->numberBetween(1, 50),
            'years_in_business' => fake()->numberBetween(1, 5),
        ]);
    }
}
