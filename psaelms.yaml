models:
  Api\Psaelms\MDA:
    id: uuid
    name: string
    address: text
    contact_person: string
    contact_email: string
    contact_phone: string
    status: string
    relationships:
      hasMany: User, LeaseAgreement, Asset

  Api\Psaelms\LeaseAgreement:
    id: uuid
    mda_id: uuid
    asset_id: uuid
    start_date: date
    end_date: date
    amount: decimal:12,2
    status: string
    relationships:
      belongsTo: MDA, Asset

  Api\Psaelms\Asset:
    id: uuid
    mda_id: uuid
    name: string
    category: string
    description: text
    purchase_date: date
    value: decimal:12,2
    status: string
    relationships:
      belongsTo: MDA
      hasMany: LeaseAgreement

controllers:
  Psaelms\MDAController:
    resource: api

  Psaelms\LeaseAgreementController:
    resource: api

  Psaelms\AssetController:
    resource: api

seeders:
  Psaelms\MDA,
  Psaelms\LeaseAgreement,
  Psaelms\Asset
