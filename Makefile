.PHONY: cc cf code-check code-fix init profile coverage hook unhook

cc: code-check
cf: code-fix

code-check:
	@sh .scripts/code-check
	@echo ""
	@sh .scripts/repo-status
	@echo "Hint: You can run 'make code-fix' to attempt to auto-fix the issues"

code-fix:
	@./.scripts/code-fix
	@echo ""
	@echo "Hint: You can also run 'make cc' and 'make cf' for checking and fixing respectively"

coverage:
	php artisan test -c phpunit.coverage.xml

profile:
	php artisan test --profile

hook: unhook
	ln -s `pwd`/.scripts/hooks/pre-commit .git/hooks/pre-commit
	ls .git/hooks/pre-commit

unhook:
	@[ -L .git/hooks/pre-commit ] \
		&& rm .git/hooks/pre-commit \
		|| echo "the pre-commit hook is not present"
